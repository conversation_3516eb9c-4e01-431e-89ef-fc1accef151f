# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 307ms
  generate-prefab-packages
    [gap of 387ms]
    exec-prefab 3649ms
    [gap of 553ms]
  generate-prefab-packages completed in 4589ms
  execute-generate-process
    exec-configure 3699ms
    [gap of 1430ms]
  execute-generate-process completed in 5132ms
  [gap of 11ms]
  remove-unexpected-so-files 18ms
  [gap of 602ms]
  write-metadata-json-to-file 30ms
generate_cxx_metadata completed in 10778ms

