# C/C++ build system timings
generate_cxx_metadata
  generate-prefab-packages
    exec-prefab 2833ms
    [gap of 138ms]
  generate-prefab-packages completed in 2973ms
  execute-generate-process
    [gap of 26ms]
    exec-configure 7720ms
    [gap of 464ms]
  execute-generate-process completed in 8210ms
  [gap of 527ms]
  write-metadata-json-to-file 42ms
  [gap of 30ms]
generate_cxx_metadata completed in 11818ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 107ms
  [gap of 123ms]
  write-metadata-json-to-file 14ms
generate_cxx_metadata completed in 254ms

