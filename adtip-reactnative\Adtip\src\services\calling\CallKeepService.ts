import <PERSON><PERSON>all<PERSON>eep from 'react-native-callkeep';
import {
  Platform,
} from 'react-native';


/**
 * CallKeepService - Manages native call UI integration
 *
 * Provides native call interface for both iOS (CallKit) and Android (ConnectionService)
 * Integrates with CallController for call state management
 */
class CallKeepService {
  private static _instance: CallKeepService;
  private isInitialized: boolean = false;
  private currentCallUUID: string | null = null;
  private eventListeners: Array<() => void> = [];

  private readonly options = {
    ios: {
      appName: 'AdTip',
    },
    android: {
      alertTitle: 'Permissions Required',
      alertDescription: 'This application needs access to your phone accounts to make calls. Please enable the phone account for AdTip in the next screen.',
      cancelButton: 'Cancel',
      okButton: 'OK',
      additionalPermissions: [],
      selfManaged: false, // Use managed connection service, not self-managed
      foregroundService: {
        channelId: 'com.adtip.calls',
        channelName: 'AdTip Calls',
        notificationTitle: 'Incoming Call',
        notificationIcon: 'ic_launcher',
      },
    },
  };

  static getInstance(): CallKeepService {
    if (!CallKeepService._instance) {
      CallKeepService._instance = new CallKeepService();
    }
    return CallKeepService._instance;
  }

  private constructor() {
    // Private constructor for singleton pattern
  }

  /**
   * Initialize CallKeep service
   */
  async initialize(): Promise<boolean> {
    if (this.isInitialized) {
      console.log('[CallKeepService] Already initialized');
      return true;
    }

    try {
      console.log('[CallKeepService] Initializing...');

      // Setup CallKeep - let it handle permissions internally
      const setupResult = await RNCallKeep.setup(this.options);
      console.log('[CallKeepService] Setup result:', setupResult);

      // Register phone account on Android
      if (Platform.OS === 'android') {
        RNCallKeep.registerPhoneAccount(this.options);
      }

      // Set up event listeners
      this.setupEventListeners();

      // Set as available
      RNCallKeep.setAvailable(true);

      this.isInitialized = true;
      console.log('[CallKeepService] Initialized successfully');
      return true;
    } catch (error) {
      console.error('[CallKeepService] Initialization failed:', error);
      return false;
    }
  }



  /**
   * Set up event listeners for CallKeep events
   */
  private setupEventListeners(): void {
    console.log('[CallKeepService] Setting up event listeners');

    // Answer call event
    const answerListener = RNCallKeep.addEventListener('answerCall', ({ callUUID }: { callUUID: string }) => {
      console.log('[CallKeepService] Call answered:', callUUID);
      this.handleCallAnswer(callUUID);
    });

    // End call event
    const endListener = RNCallKeep.addEventListener('endCall', ({ callUUID }: { callUUID: string }) => {
      console.log('[CallKeepService] Call ended:', callUUID);
      this.handleCallEnd(callUUID);
    });

    // Store listeners for cleanup (fix type issue)
    this.eventListeners.push(() => answerListener.remove(), () => endListener.remove());
  }

  /**
   * Handle call answer event
   */
  private handleCallAnswer(callUUID: string): void {
    console.log('[CallKeepService] Handling call answer for UUID:', callUUID);

    // Import CallController dynamically to avoid circular dependencies
    import('./CallController').then(({ default: CallController }) => {
      const controller = CallController.getInstance();
      controller.acceptCall();
    }).catch(error => {
      console.error('[CallKeepService] Error importing CallController for answer:', error);
    });
  }

  /**
   * Handle call end event
   */
  private handleCallEnd(callUUID: string): void {
    console.log('[CallKeepService] Handling call end for UUID:', callUUID);

    // Clear current call UUID
    if (this.currentCallUUID === callUUID) {
      this.currentCallUUID = null;
    }

    // Import CallController dynamically to avoid circular dependencies
    import('./CallController').then(({ default: CallController }) => {
      const controller = CallController.getInstance();
      controller.endCall();
    }).catch(error => {
      console.error('[CallKeepService] Error importing CallController for end:', error);
    });
  }

  /**
   * Display incoming call in native UI
   */
  async displayIncomingCall(
    uuid: string,
    handle: string,
    localizedCallerName: string,
    handleType: string = 'generic',
    hasVideo: boolean = false
  ): Promise<boolean> {
    if (!this.isInitialized) {
      console.warn('[CallKeepService] Service not initialized');
      return false;
    }

    try {
      console.log('[CallKeepService] Displaying incoming call:', {
        uuid,
        handle,
        localizedCallerName,
        handleType,
        hasVideo
      });

      RNCallKeep.displayIncomingCall(
        uuid,
        handle,
        localizedCallerName,
        handleType as any, // Fix type issue
        hasVideo
      );

      this.currentCallUUID = uuid;
      return true;
    } catch (error) {
      console.error('[CallKeepService] Failed to display incoming call:', error);
      return false;
    }
  }

  /**
   * Start outgoing call in native UI
   */
  async startCall(
    uuid: string,
    handle: string,
    localizedCallerName: string,
    handleType: string = 'generic',
    hasVideo: boolean = false
  ): Promise<boolean> {
    if (!this.isInitialized) {
      console.warn('[CallKeepService] Service not initialized');
      return false;
    }

    try {
      console.log('[CallKeepService] Starting outgoing call:', {
        uuid,
        handle,
        localizedCallerName,
        handleType,
        hasVideo
      });

      RNCallKeep.startCall(
        uuid,
        handle,
        localizedCallerName,
        handleType as any,
        hasVideo
      );

      this.currentCallUUID = uuid;
      return true;
    } catch (error) {
      console.error('[CallKeepService] Failed to start outgoing call:', error);
      return false;
    }
  }

  /**
   * End call
   */
  async endCall(uuid?: string): Promise<boolean> {
    try {
      const callUUID = uuid || this.currentCallUUID;

      if (!callUUID) {
        console.warn('[CallKeepService] No call UUID to end');
        return false;
      }

      console.log('[CallKeepService] Ending call:', callUUID);

      RNCallKeep.endCall(callUUID);

      if (this.currentCallUUID === callUUID) {
        this.currentCallUUID = null;
      }

      return true;
    } catch (error) {
      console.error('[CallKeepService] Failed to end call:', error);
      return false;
    }
  }

  /**
   * End all calls
   */
  async endAllCalls(): Promise<boolean> {
    try {
      console.log('[CallKeepService] Ending all calls');
      RNCallKeep.endAllCalls();
      this.currentCallUUID = null;
      return true;
    } catch (error) {
      console.error('[CallKeepService] Failed to end all calls:', error);
      return false;
    }
  }

  /**
   * Check if CallKeep is available on this platform
   */
  isAvailable(): boolean {
    return Platform.OS === 'android' || Platform.OS === 'ios';
  }

  /**
   * Check if CallKeep has required permissions
   * Note: CallKeep handles permissions internally during setup
   */
  async checkPermissions(): Promise<boolean> {
    if (Platform.OS === 'android') {
      try {
        // Use CallKeep's internal permission checking
        const isAvailable = await RNCallKeep.isConnectionServiceAvailable();
        const phoneAccountEnabled = await RNCallKeep.checkPhoneAccountEnabled();

        console.log('[CallKeepService] Permission status:', {
          isAvailable,
          phoneAccountEnabled
        });

        return isAvailable && phoneAccountEnabled;
      } catch (error) {
        console.error('[CallKeepService] Permission check failed:', error);
        return false;
      }
    }

    // iOS permissions are handled by the system
    return true;
  }

  /**
   * Get current call UUID
   */
  getCurrentCallUUID(): string | null {
    return this.currentCallUUID;
  }

  /**
   * Set call as connected
   */
  async setCallConnected(uuid?: string): Promise<boolean> {
    try {
      const callUUID = uuid || this.currentCallUUID;

      if (!callUUID) {
        console.warn('[CallKeepService] No call UUID to set as connected');
        return false;
      }

      console.log('[CallKeepService] Setting call as connected:', callUUID);
      RNCallKeep.setCurrentCallActive(callUUID);
      return true;
    } catch (error) {
      console.error('[CallKeepService] Failed to set call as connected:', error);
      return false;
    }
  }

  /**
   * Cleanup service and remove listeners
   */
  cleanup(): void {
    console.log('[CallKeepService] Cleaning up');

    // Remove all event listeners
    this.eventListeners.forEach(removeListener => removeListener());
    this.eventListeners = [];

    // Clear current call
    this.currentCallUUID = null;

    // Reset initialization flag
    this.isInitialized = false;
  }
}

export default CallKeepService;
