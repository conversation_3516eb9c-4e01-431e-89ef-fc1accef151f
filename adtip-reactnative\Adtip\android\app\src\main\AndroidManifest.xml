<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android" xmlns:tools="http://schemas.android.com/tools">

    <!-- BASIC NETWORK PERMISSIONS -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />

    <!-- WHATSAPP-LIKE CALLING PERMISSIONS - Minimal set -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" tools:targetApi="upsideDownCake"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CAMERA" tools:targetApi="upsideDownCake"/>
    
    <!-- VIDEOSDK & OVERLAY PERMISSIONS -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <!-- BLUETOOTH PERMISSIONS -->
    <uses-permission android:name="android.permission.BLUETOOTH" android:maxSdkVersion="30" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" android:maxSdkVersion="30" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN"
                     android:usesPermissionFlags="neverForLocation"
                     tools:targetApi="s" />

    <!-- LOCATION PERMISSIONS -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

    <!-- STORAGE PERMISSIONS -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" 
                     android:maxSdkVersion="28" 
                     tools:ignore="ScopedStorage" 
                     tools:replace="android:maxSdkVersion"
                     />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" tools:targetApi="tiramisu" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" tools:targetApi="tiramisu" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" tools:targetApi="tiramisu" />

    <!-- NOTIFICATIONS -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" tools:targetApi="tiramisu" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_PHONE_CALL" />
    <uses-permission android:name="android.permission.MANAGE_OWN_CALLS" />

    <!-- CALLKEEP PERMISSIONS -->
    <uses-permission android:name="android.permission.BIND_TELECOM_CONNECTION_SERVICE" />
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <!-- ADD_VOICEMAIL removed - not used by core app functionality -->
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />

    <!-- Camera features -->
    <uses-feature android:name="android.hardware.camera" android:required="false" />
    <uses-feature android:name="android.hardware.camera.autofocus" android:required="false" />
    <uses-feature android:name="android.hardware.microphone" android:required="false" />

    <!-- AD TRACKING -->
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />

    <!-- SMS permissions removed - not used by core app functionality -->
    <!-- OTP verification is handled via API calls, not SMS reading -->
    <!-- Razorpay auto-fill is disabled in rzp_config_checkout.json -->

    <!-- Explicitly remove problematic permissions that may be added by dependencies -->
    <uses-permission android:name="android.permission.RECEIVE_SMS" tools:node="remove" />
    <uses-permission android:name="android.permission.READ_SMS" tools:node="remove" />
    <uses-permission android:name="android.permission.READ_PHONE_NUMBERS" tools:node="remove" />
    <uses-permission android:name="android.permission.ADD_VOICEMAIL" tools:node="remove" />
    <uses-permission android:name="android.permission.READ_CALL_LOG" tools:node="remove" />

    <application
        android:name=".MainApplication"
        android:label="@string/app_name"
        android:icon="@mipmap/ic_launcher"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:allowBackup="false"
        android:theme="@style/AppTheme"
        android:supportsRtl="true"
        android:usesCleartextTraffic="true"
        tools:replace="android:allowBackup">

        <meta-data
            android:name="android.app.extract_native_libs"
            android:value="true" />

        <!-- AdMob App ID - PubScale Only -->
        <!-- Note: Simplified ad system uses only PubScale ad units -->
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-3206456546664189~6654042212"
            tools:replace="android:value" />

        <!-- VIDEOSDK FOREGROUND SERVICE CONFIGURATION -->
        <meta-data
            android:name="live.videosdk.rnfgservice.notification_channel_name"
            android:value="Meeting Notification" />
        <meta-data
            android:name="live.videosdk.rnfgservice.notification_channel_description"
            android:value="Whenever meeting started notification will appear." />
        <meta-data
            android:name="live.videosdk.rnfgservice.notification_color"
            android:resource="@android:color/holo_red_dark" />

        <!-- Firebase messaging default notification channel -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_channel_id"
            android:value="adtip_call_channel"
            tools:replace="android:value" />

        <activity
            android:name=".MainActivity"
            android:label="@string/app_name"
            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
            android:launchMode="singleTask"
            android:windowSoftInputMode="adjustPan"
            android:exported="true"
            android:showWhenLocked="true"
            android:turnScreenOn="true"> 
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <!-- Custom URL Scheme Deep Links -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="adtip" />
            </intent-filter>

            <!-- Universal Links / App Links for adtip.in -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="https"
                      android:host="adtip.in" />
            </intent-filter>

            <!-- Universal Links for www.adtip.in -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="https"
                      android:host="www.adtip.in" />
            </intent-filter>

            <!-- Universal Links for app.adtip.in -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="https"
                      android:host="app.adtip.in" />
            </intent-filter>

            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="https" />
                <data android:host="adtip.com" />
            </intent-filter>

        </activity>

        <!-- VIDEOSDK FOREGROUND SERVICE -->
        <service 
            android:name="live.videosdk.rnfgservice.ForegroundService" 
            android:foregroundServiceType="camera|microphone" 
            android:exported="false" />

        <!-- Notifee's foreground service declaration for camera and microphone -->
        <service android:name="app.notifee.core.ForegroundService" android:foregroundServiceType="camera|microphone|phoneCall" android:exported="false" />

        <!-- CALLLEEP CONNECTION SERVICE -->
        <service
            android:name="io.wazo.callkeep.VoiceConnectionService"
            android:label="VoiceConnectionService"
            android:permission="android.permission.BIND_TELECOM_CONNECTION_SERVICE"
            android:foregroundServiceType="phoneCall|camera|microphone"
            android:exported="true">
            <intent-filter>
                <action android:name="android.telecom.ConnectionService" />
            </intent-filter>
            <meta-data
                android:name="android.telecom.CONNECTION_SERVICE"
                android:value="android.telecom.ConnectionService" />
        </service>
        <service android:name="io.wazo.callkeep.RNCallKeepBackgroundMessagingService" />

        <!-- ADTIP FIREBASE MESSAGING SERVICE -->
        <service
            android:name="com.adtip.app.adtip_app.AdtipFirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <!-- ADTIP CALL RINGING SERVICE -->
        <service
            android:name="com.adtip.app.adtip_app.CallRingingService"
            android:exported="false" />

    </application>

</manifest>