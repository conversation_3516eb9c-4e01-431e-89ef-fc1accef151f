# C/C++ build system timings
generate_cxx_metadata
  [gap of 10ms]
  create-invalidation-state 51ms
  generate-prefab-packages
    [gap of 217ms]
    exec-prefab 2106ms
    [gap of 247ms]
  generate-prefab-packages completed in 2570ms
  execute-generate-process
    exec-configure 2562ms
    [gap of 423ms]
  execute-generate-process completed in 2992ms
  [gap of 168ms]
generate_cxx_metadata completed in 5807ms

