# CallKeep Permission Fix

## Problem
The CallKeep permission alert was appearing repeatedly even after permissions were granted. This was caused by conflicting permission handling between our custom permission requests and CallKeep's internal permission system.

## Root Cause Analysis

### 1. Conflicting Permission Handling
- **Issue**: Our `CallKeepService` was manually requesting `READ_PHONE_STATE` and `CALL_PHONE` permissions using `PermissionsAndroid.requestMultiple()`
- **Conflict**: CallKeep library has its own internal permission handling system that manages permissions during the `setup()` call
- **Result**: Double permission requests and confusion in permission state

### 2. Manifest Permission Issues
- **Issue**: `READ_PHONE_STATE` permission was declared in the wrong section of AndroidManifest.xml
- **Problem**: It was placed near permissions being explicitly removed with `tools:node="remove"`
- **Result**: Potential permission conflicts and unclear permission status

### 3. Incorrect Permission Checking
- **Issue**: Using manual `PermissionsAndroid` checks instead of CallKeep's internal permission methods
- **Problem**: CallKeep has specific methods like `isConnectionServiceAvailable()` and `checkPhoneAccountEnabled()`
- **Result**: Inaccurate permission status reporting

## Solution Implemented

### 1. Removed Manual Permission Requests
**File**: `src/services/calling/CallKeepService.ts`

**Before**:
```typescript
// Manual permission request in initialize()
const granted = await PermissionsAndroid.requestMultiple([
  PermissionsAndroid.PERMISSIONS.READ_PHONE_STATE,
  PermissionsAndroid.PERMISSIONS.CALL_PHONE,
]);
```

**After**:
```typescript
// Let CallKeep handle permissions internally
const setupResult = await RNCallKeep.setup(this.options);
```

**Changes**:
- ✅ Removed `requestAndroidPermissions()` method
- ✅ Removed manual `PermissionsAndroid.requestMultiple()` calls
- ✅ Let CallKeep handle permissions during `setup()`
- ✅ Removed unused imports (`PermissionsAndroid`, `Alert`, `Linking`, `uuidv4`)

### 2. Fixed Manifest Permission Declaration
**File**: `android/app/src/main/AndroidManifest.xml`

**Before**:
```xml
<!-- CALLLEEP SELF-MANAGED PERMISSIONS -->
<uses-permission android:name="android.permission.BIND_TELECOM_CONNECTION_SERVICE" />
<uses-permission android:name="android.permission.CALL_PHONE" />

<!-- Later in file, near removal section -->
<uses-permission android:name="android.permission.READ_PHONE_STATE" />
```

**After**:
```xml
<!-- CALLKEEP PERMISSIONS -->
<uses-permission android:name="android.permission.BIND_TELECOM_CONNECTION_SERVICE" />
<uses-permission android:name="android.permission.CALL_PHONE" />
<uses-permission android:name="android.permission.READ_PHONE_STATE" />
```

**Changes**:
- ✅ Moved `READ_PHONE_STATE` to proper CallKeep permissions section
- ✅ Removed duplicate `READ_PHONE_STATE` declaration from removal section
- ✅ Fixed section comment from "CALLLEEP" to "CALLKEEP"
- ✅ Grouped all CallKeep permissions together

### 3. Updated Permission Checking
**File**: `src/services/calling/CallKeepService.ts`

**Before**:
```typescript
// Manual permission checking
const permissions = await PermissionsAndroid.requestMultiple([
  PermissionsAndroid.PERMISSIONS.READ_PHONE_STATE,
  PermissionsAndroid.PERMISSIONS.CALL_PHONE,
]);
```

**After**:
```typescript
// Use CallKeep's internal permission checking
const isAvailable = await RNCallKeep.isConnectionServiceAvailable();
const phoneAccountEnabled = await RNCallKeep.checkPhoneAccountEnabled();
```

**Changes**:
- ✅ Use `RNCallKeep.isConnectionServiceAvailable()` for availability check
- ✅ Use `RNCallKeep.checkPhoneAccountEnabled()` for permission status
- ✅ Proper CallKeep-specific permission validation

### 4. Enhanced CallKeep Configuration
**File**: `src/services/calling/CallKeepService.ts`

**Before**:
```typescript
android: {
  alertTitle: 'Permissions required',
  alertDescription: 'This app needs access to phone features',
  additionalPermissions: [] as string[],
}
```

**After**:
```typescript
android: {
  alertTitle: 'Permissions Required',
  alertDescription: 'This application needs access to your phone accounts to make calls. Please enable the phone account for AdTip in the next screen.',
  additionalPermissions: [],
  selfManaged: false, // Use managed connection service
}
```

**Changes**:
- ✅ Improved alert messages for better user understanding
- ✅ Added `selfManaged: false` to use managed connection service
- ✅ Clearer description of what permissions are needed

### 5. Updated Component Integration
**File**: `src/screens/tipcall/TipCallScreenSimple.tsx`

**Changes**:
- ✅ Enhanced logging to show CallKeep permission status
- ✅ Added proper error handling for CallKeep initialization
- ✅ Better user feedback about CallKeep availability

## How CallKeep Permission System Works

### CallKeep Internal Flow
1. **Setup Call**: `RNCallKeep.setup(options)` is called
2. **Permission Check**: CallKeep internally calls `checkPhoneAccountPermission()`
3. **User Prompt**: If permissions needed, CallKeep shows its own alert dialog
4. **Phone Account**: User is directed to enable phone account in system settings
5. **Validation**: CallKeep validates permissions and phone account status

### Why This Approach Works
- ✅ **Single Source of Truth**: CallKeep manages all permission logic
- ✅ **System Integration**: Proper integration with Android's ConnectionService
- ✅ **User Experience**: Consistent system dialogs instead of custom alerts
- ✅ **Reliability**: CallKeep knows exactly what permissions it needs

## Testing the Fix

### Manual Testing Steps
1. **Fresh Install**: Uninstall and reinstall the app
2. **First Call**: Try to make a call - CallKeep should show permission dialog once
3. **Grant Permissions**: Follow CallKeep's prompts to enable phone account
4. **Subsequent Calls**: No more permission dialogs should appear
5. **Verify Native UI**: Calls should use native call interface

### Automated Testing
Run the integration test:
```typescript
import CallKeepIntegrationTest from './src/services/calling/CallKeepIntegrationTest';

const test = new CallKeepIntegrationTest();
const results = await test.runIntegrationTests();
console.log('Test Results:', results);
```

## Expected Behavior After Fix

### First Time Setup
1. User makes first call
2. CallKeep shows permission dialog (once)
3. User grants permissions and enables phone account
4. Call proceeds with native UI

### Subsequent Calls
1. No permission dialogs
2. Native call UI appears immediately
3. Seamless calling experience

### Fallback Behavior
1. If CallKeep unavailable → Custom UI used
2. If permissions denied → Custom UI used
3. Graceful degradation maintained

## Key Benefits

✅ **No More Repeated Alerts**: Permission dialog appears only once
✅ **Proper System Integration**: Uses Android's native permission flow
✅ **Better User Experience**: Familiar system dialogs and settings
✅ **Reliable Permission State**: CallKeep manages permission state correctly
✅ **Maintainable Code**: Simpler permission handling logic

## Files Modified

1. `src/services/calling/CallKeepService.ts` - Removed manual permission handling
2. `android/app/src/main/AndroidManifest.xml` - Fixed permission declarations
3. `src/screens/tipcall/TipCallScreenSimple.tsx` - Enhanced error handling
4. `src/services/calling/CallKeepIntegrationTest.ts` - Updated test logic

The fix ensures that CallKeep's internal permission system is used correctly, eliminating the recurring permission alert issue while maintaining proper fallback behavior.
