{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "RNFastImageSpec_autolinked_build", "jsonFile": "directory-RNFastImageSpec_autolinked_build-Debug-26c82466c31b5d246302.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/A1/adtip-reactnative/Adtip/node_modules/@d11/react-native-fast-image/android/build/generated/source/codegen/jni", "targetIndexes": [6]}, {"build": "rnasyncstorage_autolinked_build", "jsonFile": "directory-rnasyncstorage_autolinked_build-Debug-419f8e4b720d32f8f36f.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/A1/adtip-reactnative/Adtip/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni", "targetIndexes": [14]}, {"build": "rnclipboard_autolinked_build", "jsonFile": "directory-rnclipboard_autolinked_build-Debug-2b395381cfa15cfb14f9.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/A1/adtip-reactnative/Adtip/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni", "targetIndexes": [16]}, {"build": "rnblurview_autolinked_build", "jsonFile": "directory-rnblurview_autolinked_build-Debug-c0a0f3995dde99e34801.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/blur/android/build/generated/source/codegen/jni", "targetIndexes": [15]}, {"build": "RNDateTimePickerCGen_autolinked_build", "jsonFile": "directory-RNDateTimePickerCGen_autolinked_build-Debug-2eda097d8dd8061846aa.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni", "targetIndexes": [5]}, {"build": "rnskia_autolinked_build", "jsonFile": "directory-rnskia_autolinked_build-Debug-4f5c4cd4019b65c2b53b.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/A1/adtip-reactnative/Adtip/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni", "targetIndexes": [20]}, {"build": "lottiereactnative_autolinked_build", "jsonFile": "directory-lottiereactnative_autolinked_build-Debug-c9d0d6a16ef8c5a7646a.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/A1/adtip-reactnative/Adtip/node_modules/lottie-react-native/android/build/generated/source/codegen/jni", "targetIndexes": [12]}, {"build": "Compressor_autolinked_build", "jsonFile": "directory-Compressor_autolinked_build-Debug-ad1eb14e6be75ef2ea82.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/A1/adtip-reactnative/Adtip/node_modules/react-native-compressor/android/build/generated/source/codegen/jni", "targetIndexes": [1]}, {"build": "RNDatePickerSpecs_autolinked_build", "jsonFile": "directory-RNDatePickerSpecs_autolinked_build-Debug-c29344d75ef009ab9c30.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/A1/adtip-reactnative/Adtip/node_modules/react-native-date-picker/android/build/generated/source/codegen/jni", "targetIndexes": [4]}, {"build": "rngesturehandler_codegen_autolinked_build", "jsonFile": "directory-rngesturehandler_codegen_autolinked_build-Debug-a05140ec757b53b456d1.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/A1/adtip-reactnative/Adtip/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni", "targetIndexes": [17]}, {"build": "RNGoogleMobileAdsSpec_autolinked_build", "jsonFile": "directory-RNGoogleMobileAdsSpec_autolinked_build-Debug-9137122eabf0ed94fcfd.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/A1/adtip-reactnative/Adtip/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni", "targetIndexes": [7]}, {"build": "RNCImageCropPickerSpec_autolinked_build", "jsonFile": "directory-RNCImageCropPickerSpec_autolinked_build-Debug-d093583f5bd4e6a05125.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-crop-picker/android/build/generated/source/codegen/jni", "targetIndexes": [2]}, {"build": "RNImagePickerSpec_autolinked_build", "jsonFile": "directory-RNImagePickerSpec_autolinked_build-Debug-a76582b5c70a8ed882be.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni", "targetIndexes": [8]}, {"build": "pagerview_autolinked_build", "jsonFile": "directory-pagerview_autolinked_build-Debug-c0bc4bfa7d3e11542887.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/A1/adtip-reactnative/Adtip/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni", "targetIndexes": [13]}, {"build": "RNPermissionsSpec_autolinked_build", "jsonFile": "directory-RNPermissionsSpec_autolinked_build-Debug-2c6e8b8750a6b2ed2f27.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/A1/adtip-reactnative/Adtip/node_modules/react-native-permissions/android/build/generated/source/codegen/jni", "targetIndexes": [9]}, {"build": "rnreanimated_autolinked_build", "jsonFile": "directory-rnreanimated_autolinked_build-Debug-d047830a2b7a65427ef2.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni", "targetIndexes": [18]}, {"build": "safeareacontext_autolinked_build", "jsonFile": "directory-safeareacontext_autolinked_build-Debug-50c706154790a9b03027.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni", "targetIndexes": [23]}, {"build": "rnscreens_autolinked_build", "jsonFile": "directory-rnscreens_autolinked_build-Debug-a0fcef1b58d59e4580d4.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni", "targetIndexes": [19]}, {"build": "RNSoundSpec_autolinked_build", "jsonFile": "directory-RNSoundSpec_autolinked_build-Debug-1aa4396b9ec869e10bff.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/A1/adtip-reactnative/Adtip/node_modules/react-native-sound/android/build/generated/source/codegen/jni", "targetIndexes": [10]}, {"build": "rnsvg_autolinked_build", "jsonFile": "directory-rnsvg_autolinked_build-Debug-14732585d1b2842ed83d.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni", "targetIndexes": [21]}, {"build": "RNVectorIconsSpec_autolinked_build", "jsonFile": "directory-RNVectorIconsSpec_autolinked_build-Debug-b84bc37cef84ad42b6d2.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/A1/adtip-reactnative/Adtip/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni", "targetIndexes": [11]}, {"build": "rnviewshot_autolinked_build", "jsonFile": "directory-rnviewshot_autolinked_build-Debug-b880126f9a238f41027c.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/A1/adtip-reactnative/Adtip/node_modules/react-native-view-shot/android/build/generated/source/codegen/jni", "targetIndexes": [22]}, {"build": "RNCWebViewSpec_autolinked_build", "jsonFile": "directory-RNCWebViewSpec_autolinked_build-Debug-50d7f30b9f2d099a44f0.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/A1/adtip-reactnative/Adtip/node_modules/react-native-webview/android/build/generated/source/codegen/jni", "targetIndexes": [3]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23], "name": "appmodules", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-Debug-f6fbae8d0ddf44956238.json", "name": "appmodules", "projectIndex": 0}, {"directoryIndex": 8, "id": "react_codegen_Compressor::@408161e29a6d5b274579", "jsonFile": "target-react_codegen_Compressor-Debug-28ebfc0a05d3d1d483a4.json", "name": "react_codegen_Compressor", "projectIndex": 0}, {"directoryIndex": 12, "id": "react_codegen_RNCImageCropPickerSpec::@702b02f8d2524609d414", "jsonFile": "target-react_codegen_RNCImageCropPickerSpec-Debug-2fbb154dbd9c59a67342.json", "name": "react_codegen_RNCImageCropPickerSpec", "projectIndex": 0}, {"directoryIndex": 23, "id": "react_codegen_RNCWebViewSpec::@eb48929f9f7453740a6c", "jsonFile": "target-react_codegen_RNCWebViewSpec-Debug-489f93fb17de6fef72b7.json", "name": "react_codegen_RNCWebViewSpec", "projectIndex": 0}, {"directoryIndex": 9, "id": "react_codegen_RNDatePickerSpecs::@c00f981c6e74346c63d4", "jsonFile": "target-react_codegen_RNDatePickerSpecs-Debug-411177e809be1d77259b.json", "name": "react_codegen_RNDatePickerSpecs", "projectIndex": 0}, {"directoryIndex": 5, "id": "react_codegen_RNDateTimePickerCGen::@59b70ddc31ba2f8ef1d2", "jsonFile": "target-react_codegen_RNDateTimePickerCGen-Debug-8fda492e2bbcc8d9f516.json", "name": "react_codegen_RNDateTimePickerCGen", "projectIndex": 0}, {"directoryIndex": 1, "id": "react_codegen_RNFastImageSpec::@5f53d33017f3c907f455", "jsonFile": "target-react_codegen_RNFastImageSpec-Debug-1eb158727cc42d7f95dd.json", "name": "react_codegen_RNFastImageSpec", "projectIndex": 0}, {"directoryIndex": 11, "id": "react_codegen_RNGoogleMobileAdsSpec::@c00c605517d10bc7886c", "jsonFile": "target-react_codegen_RNGoogleMobileAdsSpec-Debug-b854183fc34d87c36c5d.json", "name": "react_codegen_RNGoogleMobileAdsSpec", "projectIndex": 0}, {"directoryIndex": 13, "id": "react_codegen_RNImagePickerSpec::@f66ee9a2efecfb28bee4", "jsonFile": "target-react_codegen_RNImagePickerSpec-Debug-c41e46ec3846bffd4d73.json", "name": "react_codegen_RNImagePickerSpec", "projectIndex": 0}, {"directoryIndex": 15, "id": "react_codegen_RNPermissionsSpec::@7ad697819b753921c957", "jsonFile": "target-react_codegen_RNPermissionsSpec-Debug-d831ee090a45d5b724d0.json", "name": "react_codegen_RNPermissionsSpec", "projectIndex": 0}, {"directoryIndex": 19, "id": "react_codegen_RNSoundSpec::@a7dfabff99aa762d23e8", "jsonFile": "target-react_codegen_RNSoundSpec-Debug-8442cd38ca758e1dbaaa.json", "name": "react_codegen_RNSoundSpec", "projectIndex": 0}, {"directoryIndex": 21, "id": "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d", "jsonFile": "target-react_codegen_RNVectorIconsSpec-Debug-d58f85e9eb17af0e1101.json", "name": "react_codegen_RNVectorIconsSpec", "projectIndex": 0}, {"directoryIndex": 7, "id": "react_codegen_lottiereactnative::@0fa4dc904d7e359a99fb", "jsonFile": "target-react_codegen_lottiereactnative-Debug-91da04942e29c1041266.json", "name": "react_codegen_lottiereactnative", "projectIndex": 0}, {"directoryIndex": 14, "id": "react_codegen_pagerview::@7032a8921530ec438d60", "jsonFile": "target-react_codegen_pagerview-Debug-2023c621f819c331288f.json", "name": "react_codegen_pagerview", "projectIndex": 0}, {"directoryIndex": 2, "id": "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe", "jsonFile": "target-react_codegen_rnasyncstorage-Debug-fc9c357b8e7b6517235e.json", "name": "react_codegen_rnasyncstorage", "projectIndex": 0}, {"directoryIndex": 4, "id": "react_codegen_rnblurview::@9a34ffec6e39b5c9049e", "jsonFile": "target-react_codegen_rnblurview-Debug-2b60a54027181850ee77.json", "name": "react_codegen_rnblurview", "projectIndex": 0}, {"directoryIndex": 3, "id": "react_codegen_rnclipboard::@6385240493dfcaf22ab7", "jsonFile": "target-react_codegen_rnclipboard-Debug-652fce5061c7b1ca446c.json", "name": "react_codegen_rnclipboard", "projectIndex": 0}, {"directoryIndex": 10, "id": "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec", "jsonFile": "target-react_codegen_rngesturehandler_codegen-Debug-c7dbf7c375d09622d57b.json", "name": "react_codegen_rngesturehandler_codegen", "projectIndex": 0}, {"directoryIndex": 16, "id": "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a", "jsonFile": "target-react_codegen_rnreanimated-Debug-360c053b4767feacea96.json", "name": "react_codegen_rnreanimated", "projectIndex": 0}, {"directoryIndex": 18, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad", "jsonFile": "target-react_codegen_rnscreens-Debug-11a4c9379a9fcef5326b.json", "name": "react_codegen_rnscreens", "projectIndex": 0}, {"directoryIndex": 6, "id": "react_codegen_rnskia::@376d8504f62839611b97", "jsonFile": "target-react_codegen_rnskia-Debug-e393f6a70c5365283cc1.json", "name": "react_codegen_rnskia", "projectIndex": 0}, {"directoryIndex": 20, "id": "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65", "jsonFile": "target-react_codegen_rnsvg-Debug-af8772e9d36813dca009.json", "name": "react_codegen_rnsvg", "projectIndex": 0}, {"directoryIndex": 22, "id": "react_codegen_rnviewshot::@0ba03d237e60b9258a87", "jsonFile": "target-react_codegen_rnviewshot-Debug-9576c50cde857ba41791.json", "name": "react_codegen_rnviewshot", "projectIndex": 0}, {"directoryIndex": 17, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a", "jsonFile": "target-react_codegen_safeareacontext-Debug-7ad1ceb93f47851b9cab.json", "name": "react_codegen_safeareacontext", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/32m733b7/arm64-v8a", "source": "C:/A1/adtip-reactnative/Adtip/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}