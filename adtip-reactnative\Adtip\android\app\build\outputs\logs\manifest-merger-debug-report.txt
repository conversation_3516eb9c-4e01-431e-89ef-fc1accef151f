-- Merging decision tree log ---
manifest
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:2:1-216:12
MERGED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:2:1-216:12
INJECTED from C:\A1\adtip-reactnative\Adtip\android\app\src\debug\AndroidManifest.xml:2:1-9:12
INJECTED from C:\A1\adtip-reactnative\Adtip\android\app\src\debug\AndroidManifest.xml:2:1-9:12
INJECTED from C:\A1\adtip-reactnative\Adtip\android\app\src\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:react-native-community_datetimepicker] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-community\datetimepicker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:lottie-react-native] C:\A1\adtip-reactnative\Adtip\node_modules\lottie-react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-compressor] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-compressor\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-gesture-handler] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-google-mobile-ads] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-26:12
MERGED from [:react-native-pager-view] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-pager-view\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-permissions] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-permissions\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-safe-area-context] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-screens] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-sound] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-sound\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-video] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-video\android\buildOutput_a15d4dee7fc4eda61b91308cbb6a2e72\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-vision-camera] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-vision-camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-webview] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-19:12
MERGED from [:d11_react-native-fast-image] C:\A1\adtip-reactnative\Adtip\node_modules\@d11\react-native-fast-image\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:notifee_react-native] C:\A1\adtip-reactnative\Adtip\node_modules\@notifee\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:nozbe_watermelondb] C:\A1\adtip-reactnative\Adtip\node_modules\@nozbe\watermelondb\native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-async-storage_async-storage] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-clipboard_clipboard] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-clipboard\clipboard\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-community_blur] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-community\blur\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-community_masked-view] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-community\masked-view\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-community_netinfo] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:react-native-community_progress-bar-android] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-community\progress-bar-android\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-firebase_analytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-41:12
MERGED from [:react-native-firebase_auth] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-11:12
MERGED from [:react-native-firebase_crashlytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-21:12
MERGED from [:react-native-firebase_database] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\database\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-11:12
MERGED from [:react-native-firebase_firestore] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\firestore\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-49:12
MERGED from [:react-native-firebase_storage] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-11:12
MERGED from [:react-native-firebase_app] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-30:12
MERGED from [:shopify_react-native-skia] C:\A1\adtip-reactnative\Adtip\node_modules\@shopify\react-native-skia\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:3:1-8:12
MERGED from [:videosdk.live_react-native-webrtc] C:\A1\adtip-reactnative\Adtip\node_modules\@videosdk.live\react-native-webrtc\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-12:12
MERGED from [:react-native-callkeep] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-callkeep\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-14:12
MERGED from [:react-native-create-thumbnail] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-create-thumbnail\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:react-native-date-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-date-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-fs] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-fs\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:react-native-geolocation-service] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-geolocation-service\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-get-random-values] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-get-random-values\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-image-crop-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-47:12
MERGED from [:react-native-image-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-19:12
MERGED from [:react-native-linear-gradient] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-linear-gradient\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-orientation-locker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-orientation-locker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-razorpay] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-13:12
MERGED from [:react-native-reanimated] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-restart] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-restart\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-svg] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-vector-icons] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-view-shot] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-view-shot\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:rnincallmanager] C:\A1\adtip-reactnative\Adtip\node_modules\@videosdk.live\react-native-incallmanager\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\70054bad7b567e49310c48ab88316206\transformed\jetified-react-android-0.79.2-debug\AndroidManifest.xml:2:1-24:12
MERGED from [com.pubscale.sdkone:offerwall:1.0.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:2:1-30:12
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:17:1-66:12
MERGED from [com.github.banketree:AndroidLame-kotlin:v0.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\43ca8b91167cc3f114f1e31d2dfcc3fd\transformed\jetified-AndroidLame-kotlin-v0.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.razorpay:checkout:1.6.41] C:\Users\<USER>\.gradle\caches\8.13\transforms\3561dfd431ca9300f3ccd6ca17af54f0\transformed\jetified-checkout-1.6.41\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd11809084d63593099820b405d61701\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:2:1-72:12
MERGED from [com.pubscale.caterpillar:analytics:0.23] C:\Users\<USER>\.gradle\caches\8.13\transforms\6da9fcb4b3cdec330f337d527696430e\transformed\jetified-analytics-0.23\AndroidManifest.xml:2:1-25:12
MERGED from [com.google.firebase:firebase-crashlytics-ndk:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\aeb8af0b6421a1d3b2ea7118ef2989f3\transformed\jetified-firebase-crashlytics-ndk-19.4.4\AndroidManifest.xml:23:1-38:12
MERGED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1756d0e485a053b668d9b50ad4f3232e\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04861295927f29f4c31729c0480972d1\transformed\jetified-firebase-analytics-22.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da392714e2b4001ad5e92291804d3aa6\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:17:1-39:12
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:17:1-75:12
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a460d6b3936d3e351c6512e5610dcf7\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:15:1-38:12
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0462f5e85e6a2044c6a7a07e46b26c72\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d9b7021a76b8c370dc1a55cf6e33d91\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:15:1-39:12
MERGED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e3d2eaa1583aa636ad7200f2b7ab13e0\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:15:1-35:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\943c4eb5cff9afe2e09fe6fa01f0c45d\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e25a4e9ce0a57f7ae5d2347b9eeb0127\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:15:1-34:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7154a9c5f3c74624ae64a9d79896bb9e\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.14.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\819885695693bbcd94c9388920867bc1\transformed\jetified-okhttp3-integration-4.14.2\AndroidManifest.xml:2:1-15:12
MERGED from [com.github.yalantis:ucrop:2.2.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7e10b62d187296d864392ee64da0a65\transformed\jetified-ucrop-2.2.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\78ac3fda40d25e87ec6da10f5419cce4\transformed\jetified-imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d5f46625db057ef05d0109c6c5ff3a42\transformed\jetified-media3-exoplayer-smoothstreaming-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4083f4489864320fdd6b580d3f6bdd3a\transformed\jetified-media3-exoplayer-dash-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fbfcdd725bcf8ac85a7f3f18643d8d95\transformed\jetified-media3-exoplayer-hls-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\91b5c4a59b6d6a7985119f29ba44ddae\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-ui:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e1703580f38993d5096d25ba35ecdf1\transformed\jetified-media3-ui-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-session:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9eda951e918e947e7d9ddd1ba9b2c563\transformed\jetified-media3-session-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\eeb03681ea24d8a026ffa7aff759128b\transformed\jetified-media3-datasource-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\488a6e4d60349704b6d07fc668295e48\transformed\jetified-media3-extractor-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb0b45adba77a142e3e783f3ceff745b\transformed\jetified-media3-container-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\94b244858ed62de794efeebfb057c98f\transformed\jetified-media3-decoder-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e11726acf37436fbf37d85e255145a47\transformed\jetified-media3-database-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3109e248783e3dafcbffb05f3029995a\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-datasource-okhttp:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bcb5d69659160e5a82b59d2393038c0d\transformed\jetified-media3-datasource-okhttp-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\72b0f839adbd62970af4fc893cad0618\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\27abbcfab09739838c65ee0a05264ec1\transformed\jetified-barcode-scanning-common-17.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ac0cc6b1d80a9a5d39c72b858381152\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9abbf9a46284e5d98ed99dd592794407\transformed\jetified-common-18.11.0\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8bcdce69c2e38ab05a17e87b8e240c89\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b5b5af68370d2b9a9c88be9dd6fee4c\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:15:1-35:12
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b176b15869f40e453b9776d3fced4c1\transformed\jetified-firebase-config-interop-16.0.1\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\eabb388f0f5b75242e6f4e74de536f0e\transformed\jetified-firebase-encoders-json-18.0.1\AndroidManifest.xml:15:1-22:12
MERGED from [androidx.work:work-runtime-ktx:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d25c683ba2a5f4e8fc44079c57ede50\transformed\work-runtime-ktx-2.8.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-ads:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9fc1356237c73a36541687fd82ee2b5\transformed\jetified-play-services-ads-24.3.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:17:1-115:12
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.room:room-ktx:2.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf21f91f640e4132fe219ebff54b55a7\transformed\jetified-room-ktx-2.5.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime:2.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e2acb9bee0a5b872b1d515edf8b6f46\transformed\room-runtime-2.5.2\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfbd6dfbd7eb9930845c16c988ee8525\transformed\jetified-credentials-1.2.0-rc01\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:17:1-44:12
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\760dd814d105a65423f5f86291fd3871\transformed\jetified-googleid-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.airbnb.android:lottie:6.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\2442db8ea23b05bae961f29ce182b42a\transformed\jetified-lottie-6.5.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a16c5c2c9cdea968e707ccfe0d81978\transformed\jetified-datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\a97a0244ead91994bae9d4a822ce48b2\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\edf5a19ba2adf1c85091121ced6da3d9\transformed\jetified-datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b587af44bbcbc68fefe9a64cb17155c\transformed\jetified-camera-extensions-1.5.0-alpha03\AndroidManifest.xml:17:1-34:12
MERGED from [androidx.camera:camera-video:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\6177f3ee616f58e544f5843e29524985\transformed\jetified-camera-video-1.5.0-alpha03\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-lifecycle:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\f46551b3f95a282437058c04b2b8602c\transformed\jetified-camera-lifecycle-1.5.0-alpha03\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\012659820a206ff178b1dfc31d8ed905\transformed\jetified-camera-camera2-1.5.0-alpha03\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-core:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\cab56218d538a3aaadf18b40d63683ba\transformed\jetified-camera-core-1.5.0-alpha03\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-view:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\4115c2750b5ddc08fc546f1d52e12846\transformed\jetified-camera-view-1.5.0-alpha03\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f3cb842f708b4d9f96a19d0863fec6\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\951972483b6ed7761be36fb1794da2fa\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef18ad19ff26599d64ec0eff4ea7dc70\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84319fb3abfb9f4cf22d670fd740c51d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.google.android.exoplayer:exoplayer:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca18fac9e39387f0d5d0e3040d168e14\transformed\jetified-exoplayer-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9b6829f7f71dcf7ed8c3a43ef2febe5\transformed\jetified-exoplayer-ui-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\48c9b964a381869134ea34daa4e1cac2\transformed\recyclerview-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\37f3afb509584664f144d26e9d46905a\transformed\jetified-viewpager2-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.bumptech.glide:avif-integration:4.14.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\05085a2ee4d2268e47829dc5e7b8281e\transformed\jetified-avif-integration-4.14.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.github.zjupure:webpdecoder:2.6.4.14.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\28d9696d1ba03482c83101bc9186888b\transformed\jetified-webpdecoder-2.6.4.14.2\AndroidManifest.xml:2:1-15:12
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\77a05faf56ea8351de692b50b3edb09d\transformed\jetified-glide-4.15.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.gms:play-services-location:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\317bf0d4f174f8d0dff9db649b3a9702\transformed\jetified-play-services-location-18.0.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-appset:16.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4842c59922b24bb6c3a519aeb184fc80\transformed\jetified-play-services-appset-16.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b81b024f11b3ce84f83dcb40b467e5a\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\e840d8bdf20cb87b791e7b0dce365361\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-wallet:18.1.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a4b12df2937b548059e098326cd7bcc\transformed\jetified-play-services-wallet-18.1.3\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9ff25ec0ffb4ba03051c851e4be9c2cb\transformed\jetified-play-services-measurement-sdk-22.4.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3d93af0b117ed25b95c723ac599fee1\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7aeb02bf2e195ef28c81edd39b5f3a89\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\89202165de2bbd15e33f0d300b6ae279\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-identity:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4515e091d9c4918d4425c0b4fadad9ac\transformed\jetified-play-services-identity-17.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68f1c6167ded75c22bf466a708dea38f\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:17:1-38:12
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b9ae5d0ed3359da3c0f87e5da630cba1\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\31cc88bd1a811e01477d23dfca8d3955\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.ump:user-messaging-platform:3.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d782996d2c63197f7994f271ec433e72\transformed\jetified-user-messaging-platform-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e96fdf56052633f58269012fd055a27\transformed\jetified-play-services-ads-identifier-18.0.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\adacd7b5e431fdb22d892d3f47577616\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6a23358ad7c61e852a7e630e05b3f0c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae7731337058eb9eda9e6d21a8139142\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2dd03a2da4e4e80325824a48077809a5\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\427783aa1196df4b1bb2539d88538822\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.mlkit:vision-interfaces:16.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec7ee6e2417445f83061705c82beb03c\transformed\jetified-vision-interfaces-16.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\23055739e0573ccfe469dc5146f384c3\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4cb91d9788ca06ebac39fd309e1fab6f\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-installations-interop:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f6f51f16e3e4bdb0e2c4a9a46c82ae1\transformed\jetified-firebase-installations-interop-17.2.0\AndroidManifest.xml:15:1-19:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\efc34c31fe060b184c0432cf507aff39\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dc4429a347bd69bf2e56511963168424\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29570d6eb73b370021b74ccbfce09510\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\72c76177b46596e17e713cd9f3d03585\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8df680b809f8e7d220078d7456e06bb5\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42ae8010e5b4e61eec06f059aac62005\transformed\loader-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ca1dc3deb8725c12fb6d8854d2d457f\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c02c6a58638664cbfa4f398f77214d5c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f38ce429bad5964b7dacb2bfaff5ba7\transformed\jetified-exoplayer-hls-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3eb93ea15722ed11641782677ae1587d\transformed\jetified-exoplayer-smoothstreaming-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5be09b85c83219ea331a58ecbdf31289\transformed\jetified-exoplayer-dash-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\554c3e4f50d330df6656d384faa80e8e\transformed\jetified-exoplayer-rtsp-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d457dd3000de28855e41f930745b41e3\transformed\jetified-exoplayer-core-2.19.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\971aefb1c45b63a51a59784e2f6f0f2e\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c071ca435d7e4157d1f8663601ef1e49\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.2.0-alpha01] C:\Users\<USER>\.gradle\caches\8.13\transforms\62bfd621dfe7196adbe33c0a81a67cb9\transformed\swiperefreshlayout-1.2.0-alpha01\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.ads:ads-identifier:1.0.0-alpha05] C:\Users\<USER>\.gradle\caches\8.13\transforms\7254cf9bcab09a1b2621dc638a7e5871\transformed\jetified-ads-identifier-1.0.0-alpha05\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9effbe9a9224ae8f0b56f94b90e2693\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3545518f142d90ae77eddda44b88804\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4df02badba841aee3ca4782d345d696b\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c0a781ade5f7759726dec1a0903c6ea\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0cb78797bbebcc5583f5b3648f5d6ccd\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\99abf5a50fc98d18cb635d28e1940775\transformed\jetified-fresco-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\091d030d0ecfc4218a289f6c4f9c847f\transformed\jetified-drawee-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\88d805db5312ff396857f098baa1c5f3\transformed\jetified-nativeimagefilters-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f7784ba77750cd53d5327a5d41c6adf\transformed\jetified-memory-type-native-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca5f86e4746e54021b6c8f6caf770c33\transformed\jetified-memory-type-java-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6de6bbbee216ff3ceb05baebaac46afa\transformed\jetified-imagepipeline-native-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e1b18841ff76bb9014d269d451a2965\transformed\jetified-memory-type-ashmem-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\90d722366856612632b7013042974af8\transformed\jetified-imagepipeline-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d258baab63e72ddb2dac3de0b2b39c1\transformed\jetified-nativeimagetranscoder-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d14d9da48e39ed27a64484686b573e1\transformed\jetified-imagepipeline-base-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8f1f234df7a9575b3126ff6d50fec9a0\transformed\jetified-urimod-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d1239945d638394f2fa2ea034d9887f7\transformed\jetified-vito-source-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\970de0b2c371c7cf0f9620187162923c\transformed\jetified-middleware-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1c2ca7d50378944176dce7a22e614a2\transformed\jetified-ui-common-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1896cdd5e4668db8d8f93d0a62d2c59\transformed\jetified-soloader-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\72f2f97f1edd16238c17e264fdaf1dad\transformed\jetified-fbcore-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84b7b03ef8446d1de9b5d4ea3cda92f6\transformed\media-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a38d6d1afe6220c365cd79513855b8ab\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\197e971f80ea5cde1b7a09cab8fd2647\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4f7aaf1dd3251b595a6d29fbf072499\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca7051459246db1c67315c146b3cbc24\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb667b71c56725e04f12fbd8df400c00\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\d90ca647b7e079a9cca20ce5e0695cd1\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd47cb17e395838b1ad8102f427d6548\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\facb7e7221e6d5866717d8be3b94d9bd\transformed\jetified-lifecycle-service-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\d75396e545ff5e8b6de70384498a4e0f\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\88ef7ff10d1cfe00bba6c2d4e1214614\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\81895437ced6c29dbb31511b38e4b8b0\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9708445cb3f39759eddbf7eb8a24dfa\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\71f204fe940be86bf3b1a03a3572f444\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8feaa743bf52883b5bccbe748b774d2\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\a74c60921ee11cceb07fcadaf42c9d30\transformed\jetified-ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\32c6230c0475f108e440ef839e9bcc70\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9377c5ad5cc8b4838d355755ff999044\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-places-placereport:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\59e448b4935c4e36590123a745093a7c\transformed\jetified-play-services-places-placereport-17.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e34b30e2c3807979c179fdca07abbb5d\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39b735ae1272dec9f6411321da8caea6\transformed\jetified-play-services-measurement-base-22.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\40721e3c98568ef24c2bd2c8723a65bb\transformed\jetified-firebase-measurement-connector-20.0.1\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ee598c7bab1dd2859d5d01f2229eaf5\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1011d3940cd0ae6659aab4410e8bea7d\transformed\fragment-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e9e3cfd885c9b0e37a3b851bad46214\transformed\jetified-fragment-ktx-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\657e3daae72dd7874f0b043c60c6105e\transformed\jetified-activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0d63cb00ac6bfd545ea575ceef8be11\transformed\jetified-activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\96ea896024c6120d1a1a85dce56aede2\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6209f910c1bc01c6bfd83d02a0a0d1e\transformed\jetified-core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\db21677b3a01819e80addb86a0500cda\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2bb283f821c52533d234058e0f2b4463\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb4eab3a116796f2406261ab34aeaf50\transformed\jetified-ui-core-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ad6a5f68ee830401b49f7ec6ef6ee94\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\61c1a68c70ba9e29d9a0551de8cac3e0\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\939b3454ba58eaee81c8299edbbed6b3\transformed\jetified-tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.react:hermes-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6a769c22a825b14c29ec2d96d2172b0\transformed\jetified-hermes-android-0.79.2-debug\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.Dimezis:BlurView:version-2.0.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c769ee9e6d014a389db47ef5f7f593e0\transformed\jetified-BlurView-version-2.0.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b3d19fb1cf6c0d27c59fcb1b19cd6b9\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.exifinterface:exifinterface:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\169bb1fa3073498b9d2ca913ede4cd56\transformed\exifinterface-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c710acb976df3a112d2495dd3babab5f\transformed\jetified-exoplayer-datasource-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-database:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58d10d1621a15bead637047809c31557\transformed\jetified-exoplayer-database-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d709fdbd7efd07e1a499a2c0b849388\transformed\jetified-exoplayer-extractor-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\abbc9aa494560d735d534cf0f1d538fa\transformed\jetified-exoplayer-decoder-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-container:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2c3d4a381cb102ec4bb8a1df13809bf\transformed\jetified-exoplayer-container-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\77b3cefe45163bc1901ca4fc5f7182cc\transformed\jetified-exoplayer-common-2.19.1\AndroidManifest.xml:17:1-26:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\19a062cc58ca0144627e4605746e2594\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bebd9d4895328a627b6a12462063f75b\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:15:1-39:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa89a3c9324ea04452492f3cae338a2f\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6fad05167c4309eb9d3d503e9503016a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c03edf9c7d4eeed7a5d0ced934b7ec1\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c33465fa9d621cd2aab064bc43ec165a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\526cbaa91020faaa599603e1c4a03d21\transformed\jetified-gifdecoder-4.15.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.ads:ads-identifier-common:1.0.0-alpha05] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb805fa6f99a9860490971d2cec19714\transformed\jetified-ads-identifier-common-1.0.0-alpha05\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.datatransport:transport-api:3.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a83ea50fb5dd787e1c6cb86ed8f32c0\transformed\jetified-transport-api-3.2.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.sqlite:sqlite-framework:2.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44856dc4c7d2bf041bb1d39c0fd95df1\transformed\sqlite-framework-2.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\10cb003b54b62f570d59d1e9b1a41c21\transformed\sqlite-2.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32d5b301c4730608d020d9e165acf3e1\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\97947d976178ed08dcaea2b05ec1bb80\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b24c3eb51e3ed594d8ad409d854e8433\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:2:1-84:12
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8f67cdf4e707817a0adbf0f742d05914\transformed\jetified-grpc-android-1.62.2\AndroidManifest.xml:2:1-9:12
MERGED from [org.greenrobot:eventbus:3.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d45c05a6182a57fed57fc0d1b9576fa4\transformed\jetified-eventbus-3.3.1\AndroidManifest.xml:2:1-9:12
MERGED from [io.github.webrtc-sdk:android:125.6422.06.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\384e72aaed998c9eedce974536e64811\transformed\jetified-android-125.6422.06.1\AndroidManifest.xml:11:1-14:12
MERGED from [net.time4j:time4j-android:4.8-2021a] C:\Users\<USER>\.gradle\caches\8.13\transforms\9736d425ce9396e27ee12625a07f4d37\transformed\jetified-time4j-android-4.8-2021a\AndroidManifest.xml:2:1-13:12
MERGED from [com.jakewharton:process-phoenix:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1414cc992efd0fca4a6cd3c9d41ee4a2\transformed\jetified-process-phoenix-2.1.2\AndroidManifest.xml:2:1-15:12
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d81b0c8ae21d76d183ae0c44210c625\transformed\jetified-fbjni-0.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\32503beb4641a05905d0ff4f11c56937\transformed\jetified-soloader-0.12.1\AndroidManifest.xml:2:1-17:12
MERGED from [org.aomedia.avif.android:avif:0.9.3.a319893] C:\Users\<USER>\.gradle\caches\8.13\transforms\0dc490363e0aae1f70bf91d2e3e0400e\transformed\jetified-avif-0.9.3.a319893\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d1206d2a6efc99807434a9fa83aff46e\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:protolite-well-known-types:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fbebe52bdd503d9412c761f23dc9ff9c\transformed\jetified-protolite-well-known-types-18.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c2b541c81d6853fcbc79ca7ed76949c\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:2:1-21:12
	package
		INJECTED from C:\A1\adtip-reactnative\Adtip\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from C:\A1\adtip-reactnative\Adtip\android\app\src\debug\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:2:70-116
	android:versionCode
		INJECTED from C:\A1\adtip-reactnative\Adtip\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:5:5-67
MERGED from [:react-native-google-mobile-ads] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:react-native-google-mobile-ads] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:react-native-firebase_analytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-67
MERGED from [:react-native-firebase_analytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-67
MERGED from [:react-native-firebase_auth] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:react-native-firebase_auth] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:react-native-firebase_database] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\database\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:react-native-firebase_database] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\database\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:react-native-firebase_storage] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:react-native-firebase_storage] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [com.pubscale.sdkone:offerwall:1.0.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:8:5-67
MERGED from [com.pubscale.sdkone:offerwall:1.0.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:8:5-67
MERGED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:8:5-67
MERGED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:8:5-67
MERGED from [com.pubscale.caterpillar:analytics:0.23] C:\Users\<USER>\.gradle\caches\8.13\transforms\6da9fcb4b3cdec330f337d527696430e\transformed\jetified-analytics-0.23\AndroidManifest.xml:10:5-67
MERGED from [com.pubscale.caterpillar:analytics:0.23] C:\Users\<USER>\.gradle\caches\8.13\transforms\6da9fcb4b3cdec330f337d527696430e\transformed\jetified-analytics-0.23\AndroidManifest.xml:10:5-67
MERGED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1756d0e485a053b668d9b50ad4f3232e\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1756d0e485a053b668d9b50ad4f3232e\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da392714e2b4001ad5e92291804d3aa6\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da392714e2b4001ad5e92291804d3aa6\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a460d6b3936d3e351c6512e5610dcf7\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a460d6b3936d3e351c6512e5610dcf7\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0462f5e85e6a2044c6a7a07e46b26c72\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0462f5e85e6a2044c6a7a07e46b26c72\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d9b7021a76b8c370dc1a55cf6e33d91\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d9b7021a76b8c370dc1a55cf6e33d91\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\943c4eb5cff9afe2e09fe6fa01f0c45d\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\943c4eb5cff9afe2e09fe6fa01f0c45d\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b5b5af68370d2b9a9c88be9dd6fee4c\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b5b5af68370d2b9a9c88be9dd6fee4c\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3d93af0b117ed25b95c723ac599fee1\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3d93af0b117ed25b95c723ac599fee1\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68f1c6167ded75c22bf466a708dea38f\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68f1c6167ded75c22bf466a708dea38f\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2dd03a2da4e4e80325824a48077809a5\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2dd03a2da4e4e80325824a48077809a5\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\23055739e0573ccfe469dc5146f384c3\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\23055739e0573ccfe469dc5146f384c3\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e34b30e2c3807979c179fdca07abbb5d\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e34b30e2c3807979c179fdca07abbb5d\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:14:5-67
MERGED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:14:5-67
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:6:5-79
MERGED from [:react-native-google-mobile-ads] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
MERGED from [:react-native-google-mobile-ads] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
MERGED from [:react-native-community_netinfo] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [:react-native-community_netinfo] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [:react-native-firebase_analytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [:react-native-firebase_analytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [:react-native-firebase_auth] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
MERGED from [:react-native-firebase_auth] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
MERGED from [:react-native-firebase_database] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\database\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
MERGED from [:react-native-firebase_database] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\database\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
MERGED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
MERGED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
MERGED from [:react-native-firebase_storage] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
MERGED from [:react-native-firebase_storage] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
MERGED from [com.pubscale.sdkone:offerwall:1.0.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:9:5-79
MERGED from [com.pubscale.sdkone:offerwall:1.0.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:9:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da392714e2b4001ad5e92291804d3aa6\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da392714e2b4001ad5e92291804d3aa6\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a460d6b3936d3e351c6512e5610dcf7\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a460d6b3936d3e351c6512e5610dcf7\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0462f5e85e6a2044c6a7a07e46b26c72\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0462f5e85e6a2044c6a7a07e46b26c72\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d9b7021a76b8c370dc1a55cf6e33d91\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d9b7021a76b8c370dc1a55cf6e33d91\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\943c4eb5cff9afe2e09fe6fa01f0c45d\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\943c4eb5cff9afe2e09fe6fa01f0c45d\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\91b5c4a59b6d6a7985119f29ba44ddae\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\91b5c4a59b6d6a7985119f29ba44ddae\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3109e248783e3dafcbffb05f3029995a\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3109e248783e3dafcbffb05f3029995a\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b5b5af68370d2b9a9c88be9dd6fee4c\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b5b5af68370d2b9a9c88be9dd6fee4c\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:25:5-79
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3d93af0b117ed25b95c723ac599fee1\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3d93af0b117ed25b95c723ac599fee1\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68f1c6167ded75c22bf466a708dea38f\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68f1c6167ded75c22bf466a708dea38f\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2dd03a2da4e4e80325824a48077809a5\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2dd03a2da4e4e80325824a48077809a5\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\23055739e0573ccfe469dc5146f384c3\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\23055739e0573ccfe469dc5146f384c3\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d457dd3000de28855e41f930745b41e3\transformed\jetified-exoplayer-core-2.19.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d457dd3000de28855e41f930745b41e3\transformed\jetified-exoplayer-core-2.19.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e34b30e2c3807979c179fdca07abbb5d\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e34b30e2c3807979c179fdca07abbb5d\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\77b3cefe45163bc1901ca4fc5f7182cc\transformed\jetified-exoplayer-common-2.19.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\77b3cefe45163bc1901ca4fc5f7182cc\transformed\jetified-exoplayer-common-2.19.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bebd9d4895328a627b6a12462063f75b\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:20:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bebd9d4895328a627b6a12462063f75b\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:20:5-79
MERGED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:11:5-79
MERGED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:11:5-79
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8f67cdf4e707817a0adbf0f742d05914\transformed\jetified-grpc-android-1.62.2\AndroidManifest.xml:7:5-79
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8f67cdf4e707817a0adbf0f742d05914\transformed\jetified-grpc-android-1.62.2\AndroidManifest.xml:7:5-79
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:6:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:7:5-76
MERGED from [:react-native-community_netinfo] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-76
MERGED from [:react-native-community_netinfo] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-76
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:7:22-73
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:10:5-77
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:5-77
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:5-77
MERGED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:15:5-77
MERGED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:15:5-77
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:10:22-74
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:11:5-71
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:11:22-68
uses-permission#android.permission.CAMERA
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:12:5-65
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:12:22-62
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:13:5-80
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:13:22-77
uses-permission#android.permission.FOREGROUND_SERVICE_MICROPHONE
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:14:5-120
	tools:targetApi
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:14:86-118
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:14:22-85
uses-permission#android.permission.FOREGROUND_SERVICE_CAMERA
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:15:5-116
	tools:targetApi
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:15:82-114
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:15:22-81
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:18:5-78
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\70054bad7b567e49310c48ab88316206\transformed\jetified-react-android-0.79.2-debug\AndroidManifest.xml:16:5-78
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\70054bad7b567e49310c48ab88316206\transformed\jetified-react-android-0.79.2-debug\AndroidManifest.xml:16:5-78
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:18:22-75
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:19:5-68
MERGED from [:react-native-google-mobile-ads] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [:react-native-google-mobile-ads] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [:react-native-firebase_analytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-68
MERGED from [:react-native-firebase_analytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-68
MERGED from [:react-native-firebase_auth] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [:react-native-firebase_auth] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [:react-native-firebase_database] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\database\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [:react-native-firebase_database] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\database\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [:react-native-firebase_storage] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [:react-native-firebase_storage] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da392714e2b4001ad5e92291804d3aa6\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da392714e2b4001ad5e92291804d3aa6\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:24:5-68
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:23:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3d93af0b117ed25b95c723ac599fee1\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3d93af0b117ed25b95c723ac599fee1\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2dd03a2da4e4e80325824a48077809a5\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2dd03a2da4e4e80325824a48077809a5\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e34b30e2c3807979c179fdca07abbb5d\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e34b30e2c3807979c179fdca07abbb5d\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:10:5-68
MERGED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:10:5-68
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:19:22-65
uses-permission#android.permission.BLUETOOTH
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:22:5-95
	android:maxSdkVersion
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:22:66-92
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:22:22-65
uses-permission#android.permission.BLUETOOTH_ADMIN
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:23:5-101
	android:maxSdkVersion
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:23:72-98
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:23:22-71
uses-permission#android.permission.BLUETOOTH_CONNECT
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:24:5-76
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:24:22-73
uses-permission#android.permission.BLUETOOTH_SCAN
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:25:5-27:44
	android:usesPermissionFlags
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:26:22-68
	tools:targetApi
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:27:22-41
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:25:22-70
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:30:5-79
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:30:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:31:5-81
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:31:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:34:5-107
MERGED from [:react-native-create-thumbnail] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-create-thumbnail\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-80
MERGED from [:react-native-create-thumbnail] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-create-thumbnail\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-80
	android:maxSdkVersion
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:34:78-104
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:34:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:35:5-39:24
MERGED from [:react-native-create-thumbnail] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-create-thumbnail\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
MERGED from [:react-native-create-thumbnail] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-create-thumbnail\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
MERGED from [:react-native-fs] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-fs\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
MERGED from [:react-native-fs] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-fs\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
MERGED from [:react-native-image-crop-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-15:38
MERGED from [:react-native-image-crop-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-15:38
	android:maxSdkVersion
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:36:22-48
		REJECTED from [:react-native-image-crop-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-35
	tools:ignore
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:37:22-50
	tools:replace
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:38:22-59
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:35:22-78
uses-permission#android.permission.READ_MEDIA_AUDIO
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:40:5-102
	tools:targetApi
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:40:73-99
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:40:22-72
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:41:5-102
	tools:targetApi
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:41:73-99
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:41:22-72
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:42:5-103
	tools:targetApi
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:42:74-100
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:42:22-73
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:45:5-104
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:23:5-77
MERGED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:9:5-77
MERGED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:9:5-77
	tools:targetApi
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:45:75-101
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:45:22-74
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:46:5-81
REJECTED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:30:5-32:31
REJECTED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
REJECTED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:12:5-81
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:46:22-78
uses-permission#android.permission.VIBRATE
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:47:5-66
MERGED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:13:5-66
MERGED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:13:5-66
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:47:22-63
uses-permission#android.permission.FOREGROUND_SERVICE_PHONE_CALL
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:48:5-88
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:48:22-85
uses-permission#android.permission.MANAGE_OWN_CALLS
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:49:5-75
MERGED from [:react-native-callkeep] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-callkeep\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-75
MERGED from [:react-native-callkeep] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-callkeep\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-75
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:49:22-72
uses-permission#android.permission.BIND_TELECOM_CONNECTION_SERVICE
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:52:5-90
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:52:22-87
uses-permission#android.permission.CALL_PHONE
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:53:5-69
MERGED from [:react-native-callkeep] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-callkeep\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-69
MERGED from [:react-native-callkeep] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-callkeep\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-69
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:53:22-66
uses-permission#android.permission.READ_PHONE_STATE
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:54:5-75
MERGED from [:react-native-callkeep] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-callkeep\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-10:38
MERGED from [:react-native-callkeep] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-callkeep\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-10:38
	android:maxSdkVersion
		ADDED from [:react-native-callkeep] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-callkeep\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:9-35
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:54:22-72
uses-permission#android.permission.USE_FULL_SCREEN_INTENT
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:56:5-81
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:56:22-78
uses-feature#android.hardware.camera
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:59:5-85
	android:required
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:59:58-82
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:59:19-57
uses-feature#android.hardware.camera.autofocus
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:60:5-95
	android:required
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:60:68-92
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:60:19-67
uses-feature#android.hardware.microphone
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:61:5-89
	android:required
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:61:62-86
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:61:19-61
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:64:5-79
MERGED from [com.pubscale.sdkone:offerwall:1.0.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:10:5-79
MERGED from [com.pubscale.sdkone:offerwall:1.0.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:10:5-79
MERGED from [com.pubscale.caterpillar:analytics:0.23] C:\Users\<USER>\.gradle\caches\8.13\transforms\6da9fcb4b3cdec330f337d527696430e\transformed\jetified-analytics-0.23\AndroidManifest.xml:9:5-79
MERGED from [com.pubscale.caterpillar:analytics:0.23] C:\Users\<USER>\.gradle\caches\8.13\transforms\6da9fcb4b3cdec330f337d527696430e\transformed\jetified-analytics-0.23\AndroidManifest.xml:9:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da392714e2b4001ad5e92291804d3aa6\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da392714e2b4001ad5e92291804d3aa6\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3d93af0b117ed25b95c723ac599fee1\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3d93af0b117ed25b95c723ac599fee1\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e96fdf56052633f58269012fd055a27\transformed\jetified-play-services-ads-identifier-18.0.1\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e96fdf56052633f58269012fd055a27\transformed\jetified-play-services-ads-identifier-18.0.1\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e34b30e2c3807979c179fdca07abbb5d\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e34b30e2c3807979c179fdca07abbb5d\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:26:5-79
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:64:22-76
uses-permission#android.permission.RECEIVE_SMS
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:71:5-90
	tools:node
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:71:68-87
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:71:22-67
uses-permission#android.permission.READ_SMS
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:72:5-87
	tools:node
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:72:65-84
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:72:22-64
uses-permission#android.permission.READ_PHONE_NUMBERS
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:73:5-97
REJECTED from [:react-native-callkeep] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-callkeep\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-77
	tools:node
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:73:75-94
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:73:22-74
uses-permission#android.permission.ADD_VOICEMAIL
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:74:5-92
	tools:node
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:74:70-89
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:74:22-69
uses-permission#android.permission.READ_CALL_LOG
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:75:5-92
	tools:node
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:75:70-89
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:75:22-69
application
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:77:5-214:19
MERGED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:77:5-214:19
MERGED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:77:5-214:19
INJECTED from C:\A1\adtip-reactnative\Adtip\android\app\src\debug\AndroidManifest.xml:5:5-8:50
MERGED from [:react-native-community_datetimepicker] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-community\datetimepicker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-20
MERGED from [:react-native-community_datetimepicker] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-community\datetimepicker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-20
MERGED from [:react-native-google-mobile-ads] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-24:19
MERGED from [:react-native-google-mobile-ads] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-24:19
MERGED from [:react-native-webview] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:19
MERGED from [:react-native-webview] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:19
MERGED from [:notifee_react-native] C:\A1\adtip-reactnative\Adtip\node_modules\@notifee\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:notifee_react-native] C:\A1\adtip-reactnative\Adtip\node_modules\@notifee\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:react-native-firebase_analytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-39:19
MERGED from [:react-native-firebase_analytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-39:19
MERGED from [:react-native-firebase_crashlytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-19:19
MERGED from [:react-native-firebase_crashlytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-19:19
MERGED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-47:19
MERGED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-47:19
MERGED from [:react-native-firebase_app] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-28:19
MERGED from [:react-native-firebase_app] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-28:19
MERGED from [:react-native-image-crop-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:5-45:19
MERGED from [:react-native-image-crop-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:5-45:19
MERGED from [:react-native-image-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:19
MERGED from [:react-native-image-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:19
MERGED from [:react-native-razorpay] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-11:19
MERGED from [:react-native-razorpay] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-11:19
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\70054bad7b567e49310c48ab88316206\transformed\jetified-react-android-0.79.2-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\70054bad7b567e49310c48ab88316206\transformed\jetified-react-android-0.79.2-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.pubscale.sdkone:offerwall:1.0.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:22:5-28:19
MERGED from [com.pubscale.sdkone:offerwall:1.0.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:22:5-28:19
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd11809084d63593099820b405d61701\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd11809084d63593099820b405d61701\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:41:5-70:19
MERGED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:41:5-70:19
MERGED from [com.pubscale.caterpillar:analytics:0.23] C:\Users\<USER>\.gradle\caches\8.13\transforms\6da9fcb4b3cdec330f337d527696430e\transformed\jetified-analytics-0.23\AndroidManifest.xml:12:5-23:19
MERGED from [com.pubscale.caterpillar:analytics:0.23] C:\Users\<USER>\.gradle\caches\8.13\transforms\6da9fcb4b3cdec330f337d527696430e\transformed\jetified-analytics-0.23\AndroidManifest.xml:12:5-23:19
MERGED from [com.google.firebase:firebase-crashlytics-ndk:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\aeb8af0b6421a1d3b2ea7118ef2989f3\transformed\jetified-firebase-crashlytics-ndk-19.4.4\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.firebase:firebase-crashlytics-ndk:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\aeb8af0b6421a1d3b2ea7118ef2989f3\transformed\jetified-firebase-crashlytics-ndk-19.4.4\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1756d0e485a053b668d9b50ad4f3232e\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1756d0e485a053b668d9b50ad4f3232e\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04861295927f29f4c31729c0480972d1\transformed\jetified-firebase-analytics-22.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04861295927f29f4c31729c0480972d1\transformed\jetified-firebase-analytics-22.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da392714e2b4001ad5e92291804d3aa6\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:29:5-37:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da392714e2b4001ad5e92291804d3aa6\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:29:5-37:19
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a460d6b3936d3e351c6512e5610dcf7\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:25:5-36:19
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a460d6b3936d3e351c6512e5610dcf7\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:25:5-36:19
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0462f5e85e6a2044c6a7a07e46b26c72\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0462f5e85e6a2044c6a7a07e46b26c72\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d9b7021a76b8c370dc1a55cf6e33d91\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:26:5-37:19
MERGED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d9b7021a76b8c370dc1a55cf6e33d91\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:26:5-37:19
MERGED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e3d2eaa1583aa636ad7200f2b7ab13e0\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:21:5-33:19
MERGED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e3d2eaa1583aa636ad7200f2b7ab13e0\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:21:5-33:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\943c4eb5cff9afe2e09fe6fa01f0c45d\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\943c4eb5cff9afe2e09fe6fa01f0c45d\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e25a4e9ce0a57f7ae5d2347b9eeb0127\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:21:5-32:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e25a4e9ce0a57f7ae5d2347b9eeb0127\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:21:5-32:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7154a9c5f3c74624ae64a9d79896bb9e\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7154a9c5f3c74624ae64a9d79896bb9e\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.14.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\819885695693bbcd94c9388920867bc1\transformed\jetified-okhttp3-integration-4.14.2\AndroidManifest.xml:9:5-13:19
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.14.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\819885695693bbcd94c9388920867bc1\transformed\jetified-okhttp3-integration-4.14.2\AndroidManifest.xml:9:5-13:19
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\72b0f839adbd62970af4fc893cad0618\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\72b0f839adbd62970af4fc893cad0618\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ac0cc6b1d80a9a5d39c72b858381152\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ac0cc6b1d80a9a5d39c72b858381152\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9abbf9a46284e5d98ed99dd592794407\transformed\jetified-common-18.11.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9abbf9a46284e5d98ed99dd592794407\transformed\jetified-common-18.11.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8bcdce69c2e38ab05a17e87b8e240c89\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8bcdce69c2e38ab05a17e87b8e240c89\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b5b5af68370d2b9a9c88be9dd6fee4c\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:25:5-33:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b5b5af68370d2b9a9c88be9dd6fee4c\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:25:5-33:19
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:70:5-113:19
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:70:5-113:19
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.room:room-runtime:2.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e2acb9bee0a5b872b1d515edf8b6f46\transformed\room-runtime-2.5.2\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e2acb9bee0a5b872b1d515edf8b6f46\transformed\room-runtime-2.5.2\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [com.airbnb.android:lottie:6.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\2442db8ea23b05bae961f29ce182b42a\transformed\jetified-lottie-6.5.2\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\2442db8ea23b05bae961f29ce182b42a\transformed\jetified-lottie-6.5.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b587af44bbcbc68fefe9a64cb17155c\transformed\jetified-camera-extensions-1.5.0-alpha03\AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b587af44bbcbc68fefe9a64cb17155c\transformed\jetified-camera-extensions-1.5.0-alpha03\AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\012659820a206ff178b1dfc31d8ed905\transformed\jetified-camera-camera2-1.5.0-alpha03\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\012659820a206ff178b1dfc31d8ed905\transformed\jetified-camera-camera2-1.5.0-alpha03\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\cab56218d538a3aaadf18b40d63683ba\transformed\jetified-camera-core-1.5.0-alpha03\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\cab56218d538a3aaadf18b40d63683ba\transformed\jetified-camera-core-1.5.0-alpha03\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\951972483b6ed7761be36fb1794da2fa\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\951972483b6ed7761be36fb1794da2fa\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84319fb3abfb9f4cf22d670fd740c51d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84319fb3abfb9f4cf22d670fd740c51d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.github.zjupure:webpdecoder:2.6.4.14.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\28d9696d1ba03482c83101bc9186888b\transformed\jetified-webpdecoder-2.6.4.14.2\AndroidManifest.xml:9:5-13:19
MERGED from [com.github.zjupure:webpdecoder:2.6.4.14.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\28d9696d1ba03482c83101bc9186888b\transformed\jetified-webpdecoder-2.6.4.14.2\AndroidManifest.xml:9:5-13:19
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\77a05faf56ea8351de692b50b3edb09d\transformed\jetified-glide-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\77a05faf56ea8351de692b50b3edb09d\transformed\jetified-glide-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [com.google.android.gms:play-services-location:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\317bf0d4f174f8d0dff9db649b3a9702\transformed\jetified-play-services-location-18.0.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\317bf0d4f174f8d0dff9db649b3a9702\transformed\jetified-play-services-location-18.0.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4842c59922b24bb6c3a519aeb184fc80\transformed\jetified-play-services-appset-16.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4842c59922b24bb6c3a519aeb184fc80\transformed\jetified-play-services-appset-16.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9ff25ec0ffb4ba03051c851e4be9c2cb\transformed\jetified-play-services-measurement-sdk-22.4.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9ff25ec0ffb4ba03051c851e4be9c2cb\transformed\jetified-play-services-measurement-sdk-22.4.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3d93af0b117ed25b95c723ac599fee1\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3d93af0b117ed25b95c723ac599fee1\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7aeb02bf2e195ef28c81edd39b5f3a89\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7aeb02bf2e195ef28c81edd39b5f3a89\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.android.gms:play-services-identity:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4515e091d9c4918d4425c0b4fadad9ac\transformed\jetified-play-services-identity-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-identity:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4515e091d9c4918d4425c0b4fadad9ac\transformed\jetified-play-services-identity-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68f1c6167ded75c22bf466a708dea38f\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:30:5-36:19
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68f1c6167ded75c22bf466a708dea38f\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:30:5-36:19
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b9ae5d0ed3359da3c0f87e5da630cba1\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b9ae5d0ed3359da3c0f87e5da630cba1\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\31cc88bd1a811e01477d23dfca8d3955\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\31cc88bd1a811e01477d23dfca8d3955\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e96fdf56052633f58269012fd055a27\transformed\jetified-play-services-ads-identifier-18.0.1\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e96fdf56052633f58269012fd055a27\transformed\jetified-play-services-ads-identifier-18.0.1\AndroidManifest.xml:25:5-20
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\adacd7b5e431fdb22d892d3f47577616\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\adacd7b5e431fdb22d892d3f47577616\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6a23358ad7c61e852a7e630e05b3f0c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6a23358ad7c61e852a7e630e05b3f0c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae7731337058eb9eda9e6d21a8139142\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae7731337058eb9eda9e6d21a8139142\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\427783aa1196df4b1bb2539d88538822\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\427783aa1196df4b1bb2539d88538822\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4cb91d9788ca06ebac39fd309e1fab6f\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4cb91d9788ca06ebac39fd309e1fab6f\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:5:5-6:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c02c6a58638664cbfa4f398f77214d5c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c02c6a58638664cbfa4f398f77214d5c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4f7aaf1dd3251b595a6d29fbf072499\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4f7aaf1dd3251b595a6d29fbf072499\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\88ef7ff10d1cfe00bba6c2d4e1214614\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\88ef7ff10d1cfe00bba6c2d4e1214614\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\32c6230c0475f108e440ef839e9bcc70\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\32c6230c0475f108e440ef839e9bcc70\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9377c5ad5cc8b4838d355755ff999044\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9377c5ad5cc8b4838d355755ff999044\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-places-placereport:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\59e448b4935c4e36590123a745093a7c\transformed\jetified-play-services-places-placereport-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-places-placereport:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\59e448b4935c4e36590123a745093a7c\transformed\jetified-play-services-places-placereport-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39b735ae1272dec9f6411321da8caea6\transformed\jetified-play-services-measurement-base-22.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39b735ae1272dec9f6411321da8caea6\transformed\jetified-play-services-measurement-base-22.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\40721e3c98568ef24c2bd2c8723a65bb\transformed\jetified-firebase-measurement-connector-20.0.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\40721e3c98568ef24c2bd2c8723a65bb\transformed\jetified-firebase-measurement-connector-20.0.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ee598c7bab1dd2859d5d01f2229eaf5\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ee598c7bab1dd2859d5d01f2229eaf5\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ad6a5f68ee830401b49f7ec6ef6ee94\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ad6a5f68ee830401b49f7ec6ef6ee94\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.github.Dimezis:BlurView:version-2.0.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c769ee9e6d014a389db47ef5f7f593e0\transformed\jetified-BlurView-version-2.0.4\AndroidManifest.xml:9:5-20
MERGED from [com.github.Dimezis:BlurView:version-2.0.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c769ee9e6d014a389db47ef5f7f593e0\transformed\jetified-BlurView-version-2.0.4\AndroidManifest.xml:9:5-20
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bebd9d4895328a627b6a12462063f75b\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:23:5-37:19
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bebd9d4895328a627b6a12462063f75b\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:23:5-37:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c33465fa9d621cd2aab064bc43ec165a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c33465fa9d621cd2aab064bc43ec165a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\526cbaa91020faaa599603e1c4a03d21\transformed\jetified-gifdecoder-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\526cbaa91020faaa599603e1c4a03d21\transformed\jetified-gifdecoder-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:25:5-82:19
MERGED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:25:5-82:19
MERGED from [net.time4j:time4j-android:4.8-2021a] C:\Users\<USER>\.gradle\caches\8.13\transforms\9736d425ce9396e27ee12625a07f4d37\transformed\jetified-time4j-android-4.8-2021a\AndroidManifest.xml:11:5-20
MERGED from [net.time4j:time4j-android:4.8-2021a] C:\Users\<USER>\.gradle\caches\8.13\transforms\9736d425ce9396e27ee12625a07f4d37\transformed\jetified-time4j-android-4.8-2021a\AndroidManifest.xml:11:5-20
MERGED from [com.jakewharton:process-phoenix:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1414cc992efd0fca4a6cd3c9d41ee4a2\transformed\jetified-process-phoenix-2.1.2\AndroidManifest.xml:7:5-13:19
MERGED from [com.jakewharton:process-phoenix:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1414cc992efd0fca4a6cd3c9d41ee4a2\transformed\jetified-process-phoenix-2.1.2\AndroidManifest.xml:7:5-13:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\32503beb4641a05905d0ff4f11c56937\transformed\jetified-soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\32503beb4641a05905d0ff4f11c56937\transformed\jetified-soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [org.aomedia.avif.android:avif:0.9.3.a319893] C:\Users\<USER>\.gradle\caches\8.13\transforms\0dc490363e0aae1f70bf91d2e3e0400e\transformed\jetified-avif-0.9.3.a319893\AndroidManifest.xml:5:5-6:19
MERGED from [org.aomedia.avif.android:avif:0.9.3.a319893] C:\Users\<USER>\.gradle\caches\8.13\transforms\0dc490363e0aae1f70bf91d2e3e0400e\transformed\jetified-avif-0.9.3.a319893\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d1206d2a6efc99807434a9fa83aff46e\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d1206d2a6efc99807434a9fa83aff46e\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c2b541c81d6853fcbc79ca7ed76949c\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:11:5-19:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c2b541c81d6853fcbc79ca7ed76949c\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:11:5-19:19
	android:extractNativeLibs
		INJECTED from C:\A1\adtip-reactnative\Adtip\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4f7aaf1dd3251b595a6d29fbf072499\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:84:9-35
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:84:9-35
	android:label
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:79:9-41
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:79:9-41
	tools:ignore
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\debug\AndroidManifest.xml:8:9-48
	android:roundIcon
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:81:9-54
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:81:9-54
	tools:targetApi
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\debug\AndroidManifest.xml:7:9-29
	android:icon
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:80:9-43
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:80:9-43
	android:allowBackup
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:82:9-36
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:82:9-36
	android:theme
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:83:9-40
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:83:9-40
	tools:replace
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:86:9-44
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:86:9-44
	android:usesCleartextTraffic
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:85:9-44
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:78:9-40
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:78:9-40
meta-data#android.app.extract_native_libs
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:88:9-90:36
	android:value
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:90:13-33
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:89:13-59
meta-data#com.google.android.gms.ads.APPLICATION_ID
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:94:9-97:45
MERGED from [:react-native-google-mobile-ads] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-14:70
MERGED from [:react-native-google-mobile-ads] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-14:70
	tools:replace
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:97:13-42
	android:value
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:96:13-67
		REJECTED from [:react-native-google-mobile-ads] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-67
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:95:13-69
meta-data#live.videosdk.rnfgservice.notification_channel_name
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:100:9-102:52
	android:value
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:102:13-49
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:101:13-79
meta-data#live.videosdk.rnfgservice.notification_channel_description
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:103:9-105:82
	android:value
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:105:13-79
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:104:13-86
meta-data#live.videosdk.rnfgservice.notification_color
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:106:9-108:63
	android:resource
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:108:13-60
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:107:13-72
meta-data#com.google.firebase.messaging.default_notification_channel_id
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:111:9-114:45
MERGED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-43:32
MERGED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-43:32
	tools:replace
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:114:13-42
	android:value
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:113:13-47
		REJECTED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-29
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:112:13-89
activity#com.adtip.app.adtip_app.MainActivity
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:116:9-173:20
	android:label
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:118:13-45
	android:turnScreenOn
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:124:13-40
	android:showWhenLocked
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:123:13-42
	android:launchMode
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:120:13-44
	android:windowSoftInputMode
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:121:13-52
	android:exported
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:122:13-36
	android:configChanges
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:119:13-122
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:117:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:125:13-128:29
action#android.intent.action.MAIN
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:126:17-69
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:126:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:127:17-77
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:127:27-74
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:adtip
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:131:13-136:29
action#android.intent.action.VIEW
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:17-69
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:25-66
category#android.intent.category.DEFAULT
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:133:17-76
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:133:27-73
category#android.intent.category.BROWSABLE
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:134:17-78
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:134:27-75
data
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:17-48
	android:scheme
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:23-45
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:adtip.in+data:scheme:https
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:139:13-145:29
	android:autoVerify
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:139:28-53
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:www.adtip.in+data:scheme:https
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:148:13-154:29
	android:autoVerify
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:148:28-53
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:app.adtip.in+data:scheme:https
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:157:13-163:29
	android:autoVerify
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:157:28-53
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:adtip.com+data:scheme:https
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:165:13-171:29
	android:autoVerify
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:165:28-53
service#live.videosdk.rnfgservice.ForegroundService
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:176:9-179:40
	android:exported
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:179:13-37
	android:foregroundServiceType
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:178:13-62
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:177:13-71
service#app.notifee.core.ForegroundService
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:182:9-155
MERGED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:41:9-44:60
MERGED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:41:9-44:60
	android:exported
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:182:128-152
	android:foregroundServiceType
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:182:68-127
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:182:18-67
service#io.wazo.callkeep.VoiceConnectionService
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:185:9-197:19
	android:label
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:187:13-51
	android:exported
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:190:13-36
	android:permission
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:188:13-84
	android:foregroundServiceType
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:189:13-72
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:186:13-67
intent-filter#action:name:android.telecom.ConnectionService
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:191:13-193:29
action#android.telecom.ConnectionService
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:192:17-76
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:192:25-73
meta-data#android.telecom.CONNECTION_SERVICE
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:194:13-196:69
	android:value
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:196:17-66
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:195:17-66
service#io.wazo.callkeep.RNCallKeepBackgroundMessagingService
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:198:9-89
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:198:18-86
service#com.adtip.app.adtip_app.AdtipFirebaseMessagingService
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:201:9-207:19
	android:exported
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:203:13-37
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:202:13-81
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:204:13-206:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:205:17-78
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:205:25-75
service#com.adtip.app.adtip_app.CallRingingService
ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:210:9-212:40
	android:exported
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:212:13-37
	android:name
		ADDED from C:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:211:13-70
uses-sdk
INJECTED from C:\A1\adtip-reactnative\Adtip\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\A1\adtip-reactnative\Adtip\android\app\src\debug\AndroidManifest.xml
INJECTED from C:\A1\adtip-reactnative\Adtip\android\app\src\debug\AndroidManifest.xml
MERGED from [:react-native-community_datetimepicker] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-community\datetimepicker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_datetimepicker] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-community\datetimepicker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:lottie-react-native] C:\A1\adtip-reactnative\Adtip\node_modules\lottie-react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:lottie-react-native] C:\A1\adtip-reactnative\Adtip\node_modules\lottie-react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-compressor] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-compressor\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-compressor] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-compressor\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-google-mobile-ads] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-google-mobile-ads] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-pager-view] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-pager-view\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-pager-view] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-pager-view\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-permissions] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-permissions\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-permissions] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-permissions\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-sound] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-sound\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-sound] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-sound\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-video] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-video\android\buildOutput_a15d4dee7fc4eda61b91308cbb6a2e72\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-video] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-video\android\buildOutput_a15d4dee7fc4eda61b91308cbb6a2e72\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-vision-camera] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-vision-camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-vision-camera] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-vision-camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:d11_react-native-fast-image] C:\A1\adtip-reactnative\Adtip\node_modules\@d11\react-native-fast-image\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:d11_react-native-fast-image] C:\A1\adtip-reactnative\Adtip\node_modules\@d11\react-native-fast-image\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:notifee_react-native] C:\A1\adtip-reactnative\Adtip\node_modules\@notifee\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:notifee_react-native] C:\A1\adtip-reactnative\Adtip\node_modules\@notifee\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:nozbe_watermelondb] C:\A1\adtip-reactnative\Adtip\node_modules\@nozbe\watermelondb\native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:nozbe_watermelondb] C:\A1\adtip-reactnative\Adtip\node_modules\@nozbe\watermelondb\native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-clipboard_clipboard] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-clipboard\clipboard\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-clipboard_clipboard] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-clipboard\clipboard\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_blur] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-community\blur\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_blur] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-community\blur\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_masked-view] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-community\masked-view\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_masked-view] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-community\masked-view\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_netinfo] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_netinfo] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_progress-bar-android] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-community\progress-bar-android\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_progress-bar-android] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-community\progress-bar-android\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-firebase_analytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-firebase_analytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-firebase_auth] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-firebase_auth] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-firebase_crashlytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-firebase_crashlytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-firebase_database] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\database\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-firebase_database] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\database\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-firebase_firestore] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\firestore\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-firebase_firestore] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\firestore\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-firebase_storage] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-firebase_storage] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-firebase_app] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:react-native-firebase_app] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:shopify_react-native-skia] C:\A1\adtip-reactnative\Adtip\node_modules\@shopify\react-native-skia\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:shopify_react-native-skia] C:\A1\adtip-reactnative\Adtip\node_modules\@shopify\react-native-skia\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:videosdk.live_react-native-webrtc] C:\A1\adtip-reactnative\Adtip\node_modules\@videosdk.live\react-native-webrtc\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-44
MERGED from [:videosdk.live_react-native-webrtc] C:\A1\adtip-reactnative\Adtip\node_modules\@videosdk.live\react-native-webrtc\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-44
MERGED from [:react-native-callkeep] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-callkeep\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-callkeep] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-callkeep\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-create-thumbnail] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-create-thumbnail\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-create-thumbnail] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-create-thumbnail\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-date-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-date-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-date-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-date-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-fs] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-fs\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-fs] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-fs\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-geolocation-service] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-geolocation-service\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-geolocation-service] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-geolocation-service\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-get-random-values] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-get-random-values\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-get-random-values] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-get-random-values\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-image-crop-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-image-crop-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-image-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-image-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-linear-gradient] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-linear-gradient\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-linear-gradient] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-linear-gradient\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-orientation-locker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-orientation-locker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-orientation-locker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-orientation-locker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-razorpay] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-razorpay] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-restart] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-restart\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-restart] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-restart\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-vector-icons] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-vector-icons] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-view-shot] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-view-shot\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-view-shot] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-view-shot\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:rnincallmanager] C:\A1\adtip-reactnative\Adtip\node_modules\@videosdk.live\react-native-incallmanager\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:rnincallmanager] C:\A1\adtip-reactnative\Adtip\node_modules\@videosdk.live\react-native-incallmanager\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\70054bad7b567e49310c48ab88316206\transformed\jetified-react-android-0.79.2-debug\AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\70054bad7b567e49310c48ab88316206\transformed\jetified-react-android-0.79.2-debug\AndroidManifest.xml:10:5-44
MERGED from [com.pubscale.sdkone:offerwall:1.0.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:6:5-44
MERGED from [com.pubscale.sdkone:offerwall:1.0.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:20:5-44
MERGED from [com.github.banketree:AndroidLame-kotlin:v0.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\43ca8b91167cc3f114f1e31d2dfcc3fd\transformed\jetified-AndroidLame-kotlin-v0.0.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.banketree:AndroidLame-kotlin:v0.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\43ca8b91167cc3f114f1e31d2dfcc3fd\transformed\jetified-AndroidLame-kotlin-v0.0.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.razorpay:checkout:1.6.41] C:\Users\<USER>\.gradle\caches\8.13\transforms\3561dfd431ca9300f3ccd6ca17af54f0\transformed\jetified-checkout-1.6.41\AndroidManifest.xml:5:5-44
MERGED from [com.razorpay:checkout:1.6.41] C:\Users\<USER>\.gradle\caches\8.13\transforms\3561dfd431ca9300f3ccd6ca17af54f0\transformed\jetified-checkout-1.6.41\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd11809084d63593099820b405d61701\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd11809084d63593099820b405d61701\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:6:5-44
MERGED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:6:5-44
MERGED from [com.pubscale.caterpillar:analytics:0.23] C:\Users\<USER>\.gradle\caches\8.13\transforms\6da9fcb4b3cdec330f337d527696430e\transformed\jetified-analytics-0.23\AndroidManifest.xml:5:5-7:41
MERGED from [com.pubscale.caterpillar:analytics:0.23] C:\Users\<USER>\.gradle\caches\8.13\transforms\6da9fcb4b3cdec330f337d527696430e\transformed\jetified-analytics-0.23\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-crashlytics-ndk:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\aeb8af0b6421a1d3b2ea7118ef2989f3\transformed\jetified-firebase-crashlytics-ndk-19.4.4\AndroidManifest.xml:26:5-44
MERGED from [com.google.firebase:firebase-crashlytics-ndk:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\aeb8af0b6421a1d3b2ea7118ef2989f3\transformed\jetified-firebase-crashlytics-ndk-19.4.4\AndroidManifest.xml:26:5-44
MERGED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1756d0e485a053b668d9b50ad4f3232e\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1756d0e485a053b668d9b50ad4f3232e\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04861295927f29f4c31729c0480972d1\transformed\jetified-firebase-analytics-22.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04861295927f29f4c31729c0480972d1\transformed\jetified-firebase-analytics-22.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da392714e2b4001ad5e92291804d3aa6\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da392714e2b4001ad5e92291804d3aa6\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:21:5-23:151
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:21:5-23:151
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a460d6b3936d3e351c6512e5610dcf7\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a460d6b3936d3e351c6512e5610dcf7\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0462f5e85e6a2044c6a7a07e46b26c72\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0462f5e85e6a2044c6a7a07e46b26c72\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d9b7021a76b8c370dc1a55cf6e33d91\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d9b7021a76b8c370dc1a55cf6e33d91\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e3d2eaa1583aa636ad7200f2b7ab13e0\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e3d2eaa1583aa636ad7200f2b7ab13e0\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\943c4eb5cff9afe2e09fe6fa01f0c45d\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\943c4eb5cff9afe2e09fe6fa01f0c45d\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e25a4e9ce0a57f7ae5d2347b9eeb0127\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e25a4e9ce0a57f7ae5d2347b9eeb0127\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7154a9c5f3c74624ae64a9d79896bb9e\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7154a9c5f3c74624ae64a9d79896bb9e\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.14.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\819885695693bbcd94c9388920867bc1\transformed\jetified-okhttp3-integration-4.14.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.14.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\819885695693bbcd94c9388920867bc1\transformed\jetified-okhttp3-integration-4.14.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.yalantis:ucrop:2.2.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7e10b62d187296d864392ee64da0a65\transformed\jetified-ucrop-2.2.10\AndroidManifest.xml:5:5-44
MERGED from [com.github.yalantis:ucrop:2.2.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7e10b62d187296d864392ee64da0a65\transformed\jetified-ucrop-2.2.10\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\78ac3fda40d25e87ec6da10f5419cce4\transformed\jetified-imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\78ac3fda40d25e87ec6da10f5419cce4\transformed\jetified-imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d5f46625db057ef05d0109c6c5ff3a42\transformed\jetified-media3-exoplayer-smoothstreaming-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d5f46625db057ef05d0109c6c5ff3a42\transformed\jetified-media3-exoplayer-smoothstreaming-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4083f4489864320fdd6b580d3f6bdd3a\transformed\jetified-media3-exoplayer-dash-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4083f4489864320fdd6b580d3f6bdd3a\transformed\jetified-media3-exoplayer-dash-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fbfcdd725bcf8ac85a7f3f18643d8d95\transformed\jetified-media3-exoplayer-hls-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fbfcdd725bcf8ac85a7f3f18643d8d95\transformed\jetified-media3-exoplayer-hls-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\91b5c4a59b6d6a7985119f29ba44ddae\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\91b5c4a59b6d6a7985119f29ba44ddae\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e1703580f38993d5096d25ba35ecdf1\transformed\jetified-media3-ui-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e1703580f38993d5096d25ba35ecdf1\transformed\jetified-media3-ui-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-session:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9eda951e918e947e7d9ddd1ba9b2c563\transformed\jetified-media3-session-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-session:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9eda951e918e947e7d9ddd1ba9b2c563\transformed\jetified-media3-session-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\eeb03681ea24d8a026ffa7aff759128b\transformed\jetified-media3-datasource-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\eeb03681ea24d8a026ffa7aff759128b\transformed\jetified-media3-datasource-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\488a6e4d60349704b6d07fc668295e48\transformed\jetified-media3-extractor-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\488a6e4d60349704b6d07fc668295e48\transformed\jetified-media3-extractor-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb0b45adba77a142e3e783f3ceff745b\transformed\jetified-media3-container-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb0b45adba77a142e3e783f3ceff745b\transformed\jetified-media3-container-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\94b244858ed62de794efeebfb057c98f\transformed\jetified-media3-decoder-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\94b244858ed62de794efeebfb057c98f\transformed\jetified-media3-decoder-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e11726acf37436fbf37d85e255145a47\transformed\jetified-media3-database-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e11726acf37436fbf37d85e255145a47\transformed\jetified-media3-database-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3109e248783e3dafcbffb05f3029995a\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3109e248783e3dafcbffb05f3029995a\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource-okhttp:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bcb5d69659160e5a82b59d2393038c0d\transformed\jetified-media3-datasource-okhttp-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource-okhttp:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bcb5d69659160e5a82b59d2393038c0d\transformed\jetified-media3-datasource-okhttp-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\72b0f839adbd62970af4fc893cad0618\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\72b0f839adbd62970af4fc893cad0618\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\27abbcfab09739838c65ee0a05264ec1\transformed\jetified-barcode-scanning-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\27abbcfab09739838c65ee0a05264ec1\transformed\jetified-barcode-scanning-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ac0cc6b1d80a9a5d39c72b858381152\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ac0cc6b1d80a9a5d39c72b858381152\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9abbf9a46284e5d98ed99dd592794407\transformed\jetified-common-18.11.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9abbf9a46284e5d98ed99dd592794407\transformed\jetified-common-18.11.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8bcdce69c2e38ab05a17e87b8e240c89\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8bcdce69c2e38ab05a17e87b8e240c89\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b5b5af68370d2b9a9c88be9dd6fee4c\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b5b5af68370d2b9a9c88be9dd6fee4c\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b176b15869f40e453b9776d3fced4c1\transformed\jetified-firebase-config-interop-16.0.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b176b15869f40e453b9776d3fced4c1\transformed\jetified-firebase-config-interop-16.0.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\eabb388f0f5b75242e6f4e74de536f0e\transformed\jetified-firebase-encoders-json-18.0.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\eabb388f0f5b75242e6f4e74de536f0e\transformed\jetified-firebase-encoders-json-18.0.1\AndroidManifest.xml:18:5-20:41
MERGED from [androidx.work:work-runtime-ktx:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d25c683ba2a5f4e8fc44079c57ede50\transformed\work-runtime-ktx-2.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d25c683ba2a5f4e8fc44079c57ede50\transformed\work-runtime-ktx-2.8.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9fc1356237c73a36541687fd82ee2b5\transformed\jetified-play-services-ads-24.3.0\AndroidManifest.xml:21:5-23:52
MERGED from [com.google.android.gms:play-services-ads:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9fc1356237c73a36541687fd82ee2b5\transformed\jetified-play-services-ads-24.3.0\AndroidManifest.xml:21:5-23:52
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf21f91f640e4132fe219ebff54b55a7\transformed\jetified-room-ktx-2.5.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf21f91f640e4132fe219ebff54b55a7\transformed\jetified-room-ktx-2.5.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e2acb9bee0a5b872b1d515edf8b6f46\transformed\room-runtime-2.5.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e2acb9bee0a5b872b1d515edf8b6f46\transformed\room-runtime-2.5.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfbd6dfbd7eb9930845c16c988ee8525\transformed\jetified-credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfbd6dfbd7eb9930845c16c988ee8525\transformed\jetified-credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\760dd814d105a65423f5f86291fd3871\transformed\jetified-googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\760dd814d105a65423f5f86291fd3871\transformed\jetified-googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\2442db8ea23b05bae961f29ce182b42a\transformed\jetified-lottie-6.5.2\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\2442db8ea23b05bae961f29ce182b42a\transformed\jetified-lottie-6.5.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a16c5c2c9cdea968e707ccfe0d81978\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a16c5c2c9cdea968e707ccfe0d81978\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\a97a0244ead91994bae9d4a822ce48b2\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\a97a0244ead91994bae9d4a822ce48b2\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\edf5a19ba2adf1c85091121ced6da3d9\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\edf5a19ba2adf1c85091121ced6da3d9\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b587af44bbcbc68fefe9a64cb17155c\transformed\jetified-camera-extensions-1.5.0-alpha03\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b587af44bbcbc68fefe9a64cb17155c\transformed\jetified-camera-extensions-1.5.0-alpha03\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-video:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\6177f3ee616f58e544f5843e29524985\transformed\jetified-camera-video-1.5.0-alpha03\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\6177f3ee616f58e544f5843e29524985\transformed\jetified-camera-video-1.5.0-alpha03\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\f46551b3f95a282437058c04b2b8602c\transformed\jetified-camera-lifecycle-1.5.0-alpha03\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\f46551b3f95a282437058c04b2b8602c\transformed\jetified-camera-lifecycle-1.5.0-alpha03\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\012659820a206ff178b1dfc31d8ed905\transformed\jetified-camera-camera2-1.5.0-alpha03\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\012659820a206ff178b1dfc31d8ed905\transformed\jetified-camera-camera2-1.5.0-alpha03\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\cab56218d538a3aaadf18b40d63683ba\transformed\jetified-camera-core-1.5.0-alpha03\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\cab56218d538a3aaadf18b40d63683ba\transformed\jetified-camera-core-1.5.0-alpha03\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-view:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\4115c2750b5ddc08fc546f1d52e12846\transformed\jetified-camera-view-1.5.0-alpha03\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\4115c2750b5ddc08fc546f1d52e12846\transformed\jetified-camera-view-1.5.0-alpha03\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f3cb842f708b4d9f96a19d0863fec6\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f3cb842f708b4d9f96a19d0863fec6\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\951972483b6ed7761be36fb1794da2fa\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\951972483b6ed7761be36fb1794da2fa\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef18ad19ff26599d64ec0eff4ea7dc70\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef18ad19ff26599d64ec0eff4ea7dc70\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84319fb3abfb9f4cf22d670fd740c51d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84319fb3abfb9f4cf22d670fd740c51d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:exoplayer:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca18fac9e39387f0d5d0e3040d168e14\transformed\jetified-exoplayer-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca18fac9e39387f0d5d0e3040d168e14\transformed\jetified-exoplayer-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9b6829f7f71dcf7ed8c3a43ef2febe5\transformed\jetified-exoplayer-ui-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9b6829f7f71dcf7ed8c3a43ef2febe5\transformed\jetified-exoplayer-ui-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\48c9b964a381869134ea34daa4e1cac2\transformed\recyclerview-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\48c9b964a381869134ea34daa4e1cac2\transformed\recyclerview-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\37f3afb509584664f144d26e9d46905a\transformed\jetified-viewpager2-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\37f3afb509584664f144d26e9d46905a\transformed\jetified-viewpager2-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.github.bumptech.glide:avif-integration:4.14.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\05085a2ee4d2268e47829dc5e7b8281e\transformed\jetified-avif-integration-4.14.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:avif-integration:4.14.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\05085a2ee4d2268e47829dc5e7b8281e\transformed\jetified-avif-integration-4.14.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.zjupure:webpdecoder:2.6.4.14.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\28d9696d1ba03482c83101bc9186888b\transformed\jetified-webpdecoder-2.6.4.14.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.zjupure:webpdecoder:2.6.4.14.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\28d9696d1ba03482c83101bc9186888b\transformed\jetified-webpdecoder-2.6.4.14.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\77a05faf56ea8351de692b50b3edb09d\transformed\jetified-glide-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\77a05faf56ea8351de692b50b3edb09d\transformed\jetified-glide-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-location:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\317bf0d4f174f8d0dff9db649b3a9702\transformed\jetified-play-services-location-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\317bf0d4f174f8d0dff9db649b3a9702\transformed\jetified-play-services-location-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4842c59922b24bb6c3a519aeb184fc80\transformed\jetified-play-services-appset-16.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4842c59922b24bb6c3a519aeb184fc80\transformed\jetified-play-services-appset-16.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b81b024f11b3ce84f83dcb40b467e5a\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b81b024f11b3ce84f83dcb40b467e5a\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\e840d8bdf20cb87b791e7b0dce365361\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\e840d8bdf20cb87b791e7b0dce365361\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-wallet:18.1.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a4b12df2937b548059e098326cd7bcc\transformed\jetified-play-services-wallet-18.1.3\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-wallet:18.1.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a4b12df2937b548059e098326cd7bcc\transformed\jetified-play-services-wallet-18.1.3\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9ff25ec0ffb4ba03051c851e4be9c2cb\transformed\jetified-play-services-measurement-sdk-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9ff25ec0ffb4ba03051c851e4be9c2cb\transformed\jetified-play-services-measurement-sdk-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3d93af0b117ed25b95c723ac599fee1\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3d93af0b117ed25b95c723ac599fee1\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7aeb02bf2e195ef28c81edd39b5f3a89\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7aeb02bf2e195ef28c81edd39b5f3a89\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\89202165de2bbd15e33f0d300b6ae279\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\89202165de2bbd15e33f0d300b6ae279\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-identity:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4515e091d9c4918d4425c0b4fadad9ac\transformed\jetified-play-services-identity-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-identity:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4515e091d9c4918d4425c0b4fadad9ac\transformed\jetified-play-services-identity-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68f1c6167ded75c22bf466a708dea38f\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68f1c6167ded75c22bf466a708dea38f\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b9ae5d0ed3359da3c0f87e5da630cba1\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b9ae5d0ed3359da3c0f87e5da630cba1\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\31cc88bd1a811e01477d23dfca8d3955\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\31cc88bd1a811e01477d23dfca8d3955\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.ump:user-messaging-platform:3.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d782996d2c63197f7994f271ec433e72\transformed\jetified-user-messaging-platform-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.ump:user-messaging-platform:3.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d782996d2c63197f7994f271ec433e72\transformed\jetified-user-messaging-platform-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e96fdf56052633f58269012fd055a27\transformed\jetified-play-services-ads-identifier-18.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e96fdf56052633f58269012fd055a27\transformed\jetified-play-services-ads-identifier-18.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\adacd7b5e431fdb22d892d3f47577616\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\adacd7b5e431fdb22d892d3f47577616\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6a23358ad7c61e852a7e630e05b3f0c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6a23358ad7c61e852a7e630e05b3f0c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae7731337058eb9eda9e6d21a8139142\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae7731337058eb9eda9e6d21a8139142\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2dd03a2da4e4e80325824a48077809a5\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2dd03a2da4e4e80325824a48077809a5\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\427783aa1196df4b1bb2539d88538822\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\427783aa1196df4b1bb2539d88538822\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec7ee6e2417445f83061705c82beb03c\transformed\jetified-vision-interfaces-16.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec7ee6e2417445f83061705c82beb03c\transformed\jetified-vision-interfaces-16.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\23055739e0573ccfe469dc5146f384c3\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\23055739e0573ccfe469dc5146f384c3\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4cb91d9788ca06ebac39fd309e1fab6f\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4cb91d9788ca06ebac39fd309e1fab6f\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f6f51f16e3e4bdb0e2c4a9a46c82ae1\transformed\jetified-firebase-installations-interop-17.2.0\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f6f51f16e3e4bdb0e2c4a9a46c82ae1\transformed\jetified-firebase-installations-interop-17.2.0\AndroidManifest.xml:17:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\efc34c31fe060b184c0432cf507aff39\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\efc34c31fe060b184c0432cf507aff39\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dc4429a347bd69bf2e56511963168424\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dc4429a347bd69bf2e56511963168424\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29570d6eb73b370021b74ccbfce09510\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29570d6eb73b370021b74ccbfce09510\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\72c76177b46596e17e713cd9f3d03585\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\72c76177b46596e17e713cd9f3d03585\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8df680b809f8e7d220078d7456e06bb5\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8df680b809f8e7d220078d7456e06bb5\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42ae8010e5b4e61eec06f059aac62005\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42ae8010e5b4e61eec06f059aac62005\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ca1dc3deb8725c12fb6d8854d2d457f\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ca1dc3deb8725c12fb6d8854d2d457f\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c02c6a58638664cbfa4f398f77214d5c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c02c6a58638664cbfa4f398f77214d5c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f38ce429bad5964b7dacb2bfaff5ba7\transformed\jetified-exoplayer-hls-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f38ce429bad5964b7dacb2bfaff5ba7\transformed\jetified-exoplayer-hls-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3eb93ea15722ed11641782677ae1587d\transformed\jetified-exoplayer-smoothstreaming-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3eb93ea15722ed11641782677ae1587d\transformed\jetified-exoplayer-smoothstreaming-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5be09b85c83219ea331a58ecbdf31289\transformed\jetified-exoplayer-dash-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5be09b85c83219ea331a58ecbdf31289\transformed\jetified-exoplayer-dash-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\554c3e4f50d330df6656d384faa80e8e\transformed\jetified-exoplayer-rtsp-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\554c3e4f50d330df6656d384faa80e8e\transformed\jetified-exoplayer-rtsp-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d457dd3000de28855e41f930745b41e3\transformed\jetified-exoplayer-core-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d457dd3000de28855e41f930745b41e3\transformed\jetified-exoplayer-core-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\971aefb1c45b63a51a59784e2f6f0f2e\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\971aefb1c45b63a51a59784e2f6f0f2e\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c071ca435d7e4157d1f8663601ef1e49\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c071ca435d7e4157d1f8663601ef1e49\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.2.0-alpha01] C:\Users\<USER>\.gradle\caches\8.13\transforms\62bfd621dfe7196adbe33c0a81a67cb9\transformed\swiperefreshlayout-1.2.0-alpha01\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.2.0-alpha01] C:\Users\<USER>\.gradle\caches\8.13\transforms\62bfd621dfe7196adbe33c0a81a67cb9\transformed\swiperefreshlayout-1.2.0-alpha01\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.ads:ads-identifier:1.0.0-alpha05] C:\Users\<USER>\.gradle\caches\8.13\transforms\7254cf9bcab09a1b2621dc638a7e5871\transformed\jetified-ads-identifier-1.0.0-alpha05\AndroidManifest.xml:20:5-44
MERGED from [androidx.ads:ads-identifier:1.0.0-alpha05] C:\Users\<USER>\.gradle\caches\8.13\transforms\7254cf9bcab09a1b2621dc638a7e5871\transformed\jetified-ads-identifier-1.0.0-alpha05\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9effbe9a9224ae8f0b56f94b90e2693\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9effbe9a9224ae8f0b56f94b90e2693\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3545518f142d90ae77eddda44b88804\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3545518f142d90ae77eddda44b88804\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4df02badba841aee3ca4782d345d696b\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4df02badba841aee3ca4782d345d696b\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c0a781ade5f7759726dec1a0903c6ea\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c0a781ade5f7759726dec1a0903c6ea\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0cb78797bbebcc5583f5b3648f5d6ccd\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0cb78797bbebcc5583f5b3648f5d6ccd\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\99abf5a50fc98d18cb635d28e1940775\transformed\jetified-fresco-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\99abf5a50fc98d18cb635d28e1940775\transformed\jetified-fresco-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\091d030d0ecfc4218a289f6c4f9c847f\transformed\jetified-drawee-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\091d030d0ecfc4218a289f6c4f9c847f\transformed\jetified-drawee-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\88d805db5312ff396857f098baa1c5f3\transformed\jetified-nativeimagefilters-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\88d805db5312ff396857f098baa1c5f3\transformed\jetified-nativeimagefilters-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f7784ba77750cd53d5327a5d41c6adf\transformed\jetified-memory-type-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f7784ba77750cd53d5327a5d41c6adf\transformed\jetified-memory-type-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca5f86e4746e54021b6c8f6caf770c33\transformed\jetified-memory-type-java-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca5f86e4746e54021b6c8f6caf770c33\transformed\jetified-memory-type-java-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6de6bbbee216ff3ceb05baebaac46afa\transformed\jetified-imagepipeline-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6de6bbbee216ff3ceb05baebaac46afa\transformed\jetified-imagepipeline-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e1b18841ff76bb9014d269d451a2965\transformed\jetified-memory-type-ashmem-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e1b18841ff76bb9014d269d451a2965\transformed\jetified-memory-type-ashmem-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\90d722366856612632b7013042974af8\transformed\jetified-imagepipeline-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\90d722366856612632b7013042974af8\transformed\jetified-imagepipeline-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d258baab63e72ddb2dac3de0b2b39c1\transformed\jetified-nativeimagetranscoder-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d258baab63e72ddb2dac3de0b2b39c1\transformed\jetified-nativeimagetranscoder-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d14d9da48e39ed27a64484686b573e1\transformed\jetified-imagepipeline-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d14d9da48e39ed27a64484686b573e1\transformed\jetified-imagepipeline-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8f1f234df7a9575b3126ff6d50fec9a0\transformed\jetified-urimod-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8f1f234df7a9575b3126ff6d50fec9a0\transformed\jetified-urimod-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d1239945d638394f2fa2ea034d9887f7\transformed\jetified-vito-source-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d1239945d638394f2fa2ea034d9887f7\transformed\jetified-vito-source-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\970de0b2c371c7cf0f9620187162923c\transformed\jetified-middleware-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\970de0b2c371c7cf0f9620187162923c\transformed\jetified-middleware-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1c2ca7d50378944176dce7a22e614a2\transformed\jetified-ui-common-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1c2ca7d50378944176dce7a22e614a2\transformed\jetified-ui-common-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1896cdd5e4668db8d8f93d0a62d2c59\transformed\jetified-soloader-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1896cdd5e4668db8d8f93d0a62d2c59\transformed\jetified-soloader-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\72f2f97f1edd16238c17e264fdaf1dad\transformed\jetified-fbcore-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\72f2f97f1edd16238c17e264fdaf1dad\transformed\jetified-fbcore-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84b7b03ef8446d1de9b5d4ea3cda92f6\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84b7b03ef8446d1de9b5d4ea3cda92f6\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a38d6d1afe6220c365cd79513855b8ab\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a38d6d1afe6220c365cd79513855b8ab\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\197e971f80ea5cde1b7a09cab8fd2647\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\197e971f80ea5cde1b7a09cab8fd2647\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4f7aaf1dd3251b595a6d29fbf072499\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4f7aaf1dd3251b595a6d29fbf072499\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca7051459246db1c67315c146b3cbc24\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca7051459246db1c67315c146b3cbc24\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb667b71c56725e04f12fbd8df400c00\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb667b71c56725e04f12fbd8df400c00\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\d90ca647b7e079a9cca20ce5e0695cd1\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\d90ca647b7e079a9cca20ce5e0695cd1\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd47cb17e395838b1ad8102f427d6548\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd47cb17e395838b1ad8102f427d6548\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\facb7e7221e6d5866717d8be3b94d9bd\transformed\jetified-lifecycle-service-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\facb7e7221e6d5866717d8be3b94d9bd\transformed\jetified-lifecycle-service-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\d75396e545ff5e8b6de70384498a4e0f\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\d75396e545ff5e8b6de70384498a4e0f\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\88ef7ff10d1cfe00bba6c2d4e1214614\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\88ef7ff10d1cfe00bba6c2d4e1214614\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\81895437ced6c29dbb31511b38e4b8b0\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\81895437ced6c29dbb31511b38e4b8b0\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9708445cb3f39759eddbf7eb8a24dfa\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9708445cb3f39759eddbf7eb8a24dfa\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\71f204fe940be86bf3b1a03a3572f444\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\71f204fe940be86bf3b1a03a3572f444\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8feaa743bf52883b5bccbe748b774d2\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8feaa743bf52883b5bccbe748b774d2\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\a74c60921ee11cceb07fcadaf42c9d30\transformed\jetified-ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\a74c60921ee11cceb07fcadaf42c9d30\transformed\jetified-ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\32c6230c0475f108e440ef839e9bcc70\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\32c6230c0475f108e440ef839e9bcc70\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9377c5ad5cc8b4838d355755ff999044\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9377c5ad5cc8b4838d355755ff999044\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-places-placereport:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\59e448b4935c4e36590123a745093a7c\transformed\jetified-play-services-places-placereport-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-places-placereport:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\59e448b4935c4e36590123a745093a7c\transformed\jetified-play-services-places-placereport-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e34b30e2c3807979c179fdca07abbb5d\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e34b30e2c3807979c179fdca07abbb5d\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39b735ae1272dec9f6411321da8caea6\transformed\jetified-play-services-measurement-base-22.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39b735ae1272dec9f6411321da8caea6\transformed\jetified-play-services-measurement-base-22.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\40721e3c98568ef24c2bd2c8723a65bb\transformed\jetified-firebase-measurement-connector-20.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\40721e3c98568ef24c2bd2c8723a65bb\transformed\jetified-firebase-measurement-connector-20.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ee598c7bab1dd2859d5d01f2229eaf5\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ee598c7bab1dd2859d5d01f2229eaf5\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1011d3940cd0ae6659aab4410e8bea7d\transformed\fragment-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1011d3940cd0ae6659aab4410e8bea7d\transformed\fragment-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e9e3cfd885c9b0e37a3b851bad46214\transformed\jetified-fragment-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e9e3cfd885c9b0e37a3b851bad46214\transformed\jetified-fragment-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\657e3daae72dd7874f0b043c60c6105e\transformed\jetified-activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\657e3daae72dd7874f0b043c60c6105e\transformed\jetified-activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0d63cb00ac6bfd545ea575ceef8be11\transformed\jetified-activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0d63cb00ac6bfd545ea575ceef8be11\transformed\jetified-activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\96ea896024c6120d1a1a85dce56aede2\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\96ea896024c6120d1a1a85dce56aede2\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6209f910c1bc01c6bfd83d02a0a0d1e\transformed\jetified-core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6209f910c1bc01c6bfd83d02a0a0d1e\transformed\jetified-core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\db21677b3a01819e80addb86a0500cda\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\db21677b3a01819e80addb86a0500cda\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2bb283f821c52533d234058e0f2b4463\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2bb283f821c52533d234058e0f2b4463\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb4eab3a116796f2406261ab34aeaf50\transformed\jetified-ui-core-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb4eab3a116796f2406261ab34aeaf50\transformed\jetified-ui-core-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ad6a5f68ee830401b49f7ec6ef6ee94\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ad6a5f68ee830401b49f7ec6ef6ee94\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\61c1a68c70ba9e29d9a0551de8cac3e0\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\61c1a68c70ba9e29d9a0551de8cac3e0\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\939b3454ba58eaee81c8299edbbed6b3\transformed\jetified-tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\939b3454ba58eaee81c8299edbbed6b3\transformed\jetified-tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.react:hermes-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6a769c22a825b14c29ec2d96d2172b0\transformed\jetified-hermes-android-0.79.2-debug\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6a769c22a825b14c29ec2d96d2172b0\transformed\jetified-hermes-android-0.79.2-debug\AndroidManifest.xml:5:5-44
MERGED from [com.github.Dimezis:BlurView:version-2.0.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c769ee9e6d014a389db47ef5f7f593e0\transformed\jetified-BlurView-version-2.0.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.Dimezis:BlurView:version-2.0.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c769ee9e6d014a389db47ef5f7f593e0\transformed\jetified-BlurView-version-2.0.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b3d19fb1cf6c0d27c59fcb1b19cd6b9\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b3d19fb1cf6c0d27c59fcb1b19cd6b9\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\169bb1fa3073498b9d2ca913ede4cd56\transformed\exifinterface-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.exifinterface:exifinterface:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\169bb1fa3073498b9d2ca913ede4cd56\transformed\exifinterface-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c710acb976df3a112d2495dd3babab5f\transformed\jetified-exoplayer-datasource-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c710acb976df3a112d2495dd3babab5f\transformed\jetified-exoplayer-datasource-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58d10d1621a15bead637047809c31557\transformed\jetified-exoplayer-database-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58d10d1621a15bead637047809c31557\transformed\jetified-exoplayer-database-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d709fdbd7efd07e1a499a2c0b849388\transformed\jetified-exoplayer-extractor-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d709fdbd7efd07e1a499a2c0b849388\transformed\jetified-exoplayer-extractor-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\abbc9aa494560d735d534cf0f1d538fa\transformed\jetified-exoplayer-decoder-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\abbc9aa494560d735d534cf0f1d538fa\transformed\jetified-exoplayer-decoder-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-container:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2c3d4a381cb102ec4bb8a1df13809bf\transformed\jetified-exoplayer-container-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-container:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2c3d4a381cb102ec4bb8a1df13809bf\transformed\jetified-exoplayer-container-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\77b3cefe45163bc1901ca4fc5f7182cc\transformed\jetified-exoplayer-common-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\77b3cefe45163bc1901ca4fc5f7182cc\transformed\jetified-exoplayer-common-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\19a062cc58ca0144627e4605746e2594\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\19a062cc58ca0144627e4605746e2594\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bebd9d4895328a627b6a12462063f75b\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bebd9d4895328a627b6a12462063f75b\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa89a3c9324ea04452492f3cae338a2f\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa89a3c9324ea04452492f3cae338a2f\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6fad05167c4309eb9d3d503e9503016a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6fad05167c4309eb9d3d503e9503016a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c03edf9c7d4eeed7a5d0ced934b7ec1\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c03edf9c7d4eeed7a5d0ced934b7ec1\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c33465fa9d621cd2aab064bc43ec165a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c33465fa9d621cd2aab064bc43ec165a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\526cbaa91020faaa599603e1c4a03d21\transformed\jetified-gifdecoder-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\526cbaa91020faaa599603e1c4a03d21\transformed\jetified-gifdecoder-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.ads:ads-identifier-common:1.0.0-alpha05] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb805fa6f99a9860490971d2cec19714\transformed\jetified-ads-identifier-common-1.0.0-alpha05\AndroidManifest.xml:20:5-44
MERGED from [androidx.ads:ads-identifier-common:1.0.0-alpha05] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb805fa6f99a9860490971d2cec19714\transformed\jetified-ads-identifier-common-1.0.0-alpha05\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.datatransport:transport-api:3.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a83ea50fb5dd787e1c6cb86ed8f32c0\transformed\jetified-transport-api-3.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a83ea50fb5dd787e1c6cb86ed8f32c0\transformed\jetified-transport-api-3.2.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44856dc4c7d2bf041bb1d39c0fd95df1\transformed\sqlite-framework-2.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44856dc4c7d2bf041bb1d39c0fd95df1\transformed\sqlite-framework-2.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\10cb003b54b62f570d59d1e9b1a41c21\transformed\sqlite-2.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\10cb003b54b62f570d59d1e9b1a41c21\transformed\sqlite-2.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32d5b301c4730608d020d9e165acf3e1\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32d5b301c4730608d020d9e165acf3e1\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\97947d976178ed08dcaea2b05ec1bb80\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\97947d976178ed08dcaea2b05ec1bb80\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b24c3eb51e3ed594d8ad409d854e8433\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b24c3eb51e3ed594d8ad409d854e8433\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:5:5-7:41
MERGED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:5:5-7:41
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8f67cdf4e707817a0adbf0f742d05914\transformed\jetified-grpc-android-1.62.2\AndroidManifest.xml:5:5-44
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8f67cdf4e707817a0adbf0f742d05914\transformed\jetified-grpc-android-1.62.2\AndroidManifest.xml:5:5-44
MERGED from [org.greenrobot:eventbus:3.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d45c05a6182a57fed57fc0d1b9576fa4\transformed\jetified-eventbus-3.3.1\AndroidManifest.xml:5:5-7:41
MERGED from [org.greenrobot:eventbus:3.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d45c05a6182a57fed57fc0d1b9576fa4\transformed\jetified-eventbus-3.3.1\AndroidManifest.xml:5:5-7:41
MERGED from [io.github.webrtc-sdk:android:125.6422.06.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\384e72aaed998c9eedce974536e64811\transformed\jetified-android-125.6422.06.1\AndroidManifest.xml:13:3-72
MERGED from [io.github.webrtc-sdk:android:125.6422.06.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\384e72aaed998c9eedce974536e64811\transformed\jetified-android-125.6422.06.1\AndroidManifest.xml:13:3-72
MERGED from [net.time4j:time4j-android:4.8-2021a] C:\Users\<USER>\.gradle\caches\8.13\transforms\9736d425ce9396e27ee12625a07f4d37\transformed\jetified-time4j-android-4.8-2021a\AndroidManifest.xml:7:5-9:41
MERGED from [net.time4j:time4j-android:4.8-2021a] C:\Users\<USER>\.gradle\caches\8.13\transforms\9736d425ce9396e27ee12625a07f4d37\transformed\jetified-time4j-android-4.8-2021a\AndroidManifest.xml:7:5-9:41
MERGED from [com.jakewharton:process-phoenix:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1414cc992efd0fca4a6cd3c9d41ee4a2\transformed\jetified-process-phoenix-2.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.jakewharton:process-phoenix:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1414cc992efd0fca4a6cd3c9d41ee4a2\transformed\jetified-process-phoenix-2.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d81b0c8ae21d76d183ae0c44210c625\transformed\jetified-fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d81b0c8ae21d76d183ae0c44210c625\transformed\jetified-fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\32503beb4641a05905d0ff4f11c56937\transformed\jetified-soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\32503beb4641a05905d0ff4f11c56937\transformed\jetified-soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [org.aomedia.avif.android:avif:0.9.3.a319893] C:\Users\<USER>\.gradle\caches\8.13\transforms\0dc490363e0aae1f70bf91d2e3e0400e\transformed\jetified-avif-0.9.3.a319893\AndroidManifest.xml:4:5-44
MERGED from [org.aomedia.avif.android:avif:0.9.3.a319893] C:\Users\<USER>\.gradle\caches\8.13\transforms\0dc490363e0aae1f70bf91d2e3e0400e\transformed\jetified-avif-0.9.3.a319893\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d1206d2a6efc99807434a9fa83aff46e\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d1206d2a6efc99807434a9fa83aff46e\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:protolite-well-known-types:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fbebe52bdd503d9412c761f23dc9ff9c\transformed\jetified-protolite-well-known-types-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:protolite-well-known-types:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fbebe52bdd503d9412c761f23dc9ff9c\transformed\jetified-protolite-well-known-types-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c2b541c81d6853fcbc79ca7ed76949c\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c2b541c81d6853fcbc79ca7ed76949c\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:23:9-148
	android:targetSdkVersion
		INJECTED from C:\A1\adtip-reactnative\Adtip\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\A1\adtip-reactnative\Adtip\android\app\src\debug\AndroidManifest.xml
meta-data#com.google.android.gms.ads.DELAY_APP_MEASUREMENT_INIT
ADDED from [:react-native-google-mobile-ads] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-17:37
	android:value
		ADDED from [:react-native-google-mobile-ads] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-34
	android:name
		ADDED from [:react-native-google-mobile-ads] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-81
meta-data#com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION
ADDED from [:react-native-google-mobile-ads] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-20:36
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:110:9-112:36
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:110:9-112:36
	android:value
		ADDED from [:react-native-google-mobile-ads] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-33
	android:name
		ADDED from [:react-native-google-mobile-ads] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-83
meta-data#com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING
ADDED from [:react-native-google-mobile-ads] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:9-23:36
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:107:9-109:36
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:107:9-109:36
	android:value
		ADDED from [:react-native-google-mobile-ads] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-33
	android:name
		ADDED from [:react-native-google-mobile-ads] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-79
provider#com.reactnativecommunity.webview.RNCWebViewFileProvider
ADDED from [:react-native-webview] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
	android:grantUriPermissions
		ADDED from [:react-native-webview] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
	android:authorities
		ADDED from [:react-native-webview] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-64
	android:exported
		ADDED from [:react-native-webview] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
	android:name
		ADDED from [:react-native-webview] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-83
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:react-native-webview] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
	android:resource
		ADDED from [:react-native-webview] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
	android:name
		ADDED from [:react-native-webview] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
provider#io.invertase.notifee.NotifeeInitProvider
ADDED from [:notifee_react-native] C:\A1\adtip-reactnative\Adtip\node_modules\@notifee\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:40
	android:authorities
		ADDED from [:notifee_react-native] C:\A1\adtip-reactnative\Adtip\node_modules\@notifee\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-73
	android:exported
		ADDED from [:notifee_react-native] C:\A1\adtip-reactnative\Adtip\node_modules\@notifee\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
	android:initOrder
		ADDED from [:notifee_react-native] C:\A1\adtip-reactnative\Adtip\node_modules\@notifee\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [:notifee_react-native] C:\A1\adtip-reactnative\Adtip\node_modules\@notifee\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-68
meta-data#firebase_analytics_collection_enabled
ADDED from [:react-native-firebase_analytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-14:36
	android:value
		ADDED from [:react-native-firebase_analytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
	android:name
		ADDED from [:react-native-firebase_analytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-65
meta-data#firebase_analytics_collection_deactivated
ADDED from [:react-native-firebase_analytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-17:37
	android:value
		ADDED from [:react-native-firebase_analytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-34
	android:name
		ADDED from [:react-native-firebase_analytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-69
meta-data#google_analytics_adid_collection_enabled
ADDED from [:react-native-firebase_analytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-20:36
	android:value
		ADDED from [:react-native-firebase_analytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-33
	android:name
		ADDED from [:react-native-firebase_analytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-68
meta-data#google_analytics_ssaid_collection_enabled
ADDED from [:react-native-firebase_analytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:9-23:36
	android:value
		ADDED from [:react-native-firebase_analytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-33
	android:name
		ADDED from [:react-native-firebase_analytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-69
meta-data#google_analytics_automatic_screen_reporting_enabled
ADDED from [:react-native-firebase_analytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:9-26:36
	android:value
		ADDED from [:react-native-firebase_analytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-33
	android:name
		ADDED from [:react-native-firebase_analytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-79
meta-data#google_analytics_default_allow_analytics_storage
ADDED from [:react-native-firebase_analytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:9-29:36
	android:value
		ADDED from [:react-native-firebase_analytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-33
	android:name
		ADDED from [:react-native-firebase_analytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-76
meta-data#google_analytics_default_allow_ad_storage
ADDED from [:react-native-firebase_analytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:9-32:36
	android:value
		ADDED from [:react-native-firebase_analytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:13-33
	android:name
		ADDED from [:react-native-firebase_analytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:13-69
meta-data#google_analytics_default_allow_ad_user_data
ADDED from [:react-native-firebase_analytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:9-35:36
	android:value
		ADDED from [:react-native-firebase_analytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-33
	android:name
		ADDED from [:react-native-firebase_analytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-71
meta-data#google_analytics_default_allow_ad_personalization_signals
ADDED from [:react-native-firebase_analytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:9-38:36
	android:value
		ADDED from [:react-native-firebase_analytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:13-33
	android:name
		ADDED from [:react-native-firebase_analytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-85
meta-data#firebase_crashlytics_collection_enabled
ADDED from [:react-native-firebase_crashlytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:9-12:37
	android:value
		ADDED from [:react-native-firebase_crashlytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-34
	android:name
		ADDED from [:react-native-firebase_crashlytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-67
provider#io.invertase.firebase.crashlytics.ReactNativeFirebaseCrashlyticsInitProvider
ADDED from [:react-native-firebase_crashlytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-18:38
	android:authorities
		ADDED from [:react-native-firebase_crashlytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-94
	android:exported
		ADDED from [:react-native-firebase_crashlytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-37
	android:initOrder
		ADDED from [:react-native-firebase_crashlytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-35
	android:name
		ADDED from [:react-native-firebase_crashlytics] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-104
service#io.invertase.firebase.messaging.ReactNativeFirebaseMessagingHeadlessService
ADDED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-14:40
	android:exported
		ADDED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-37
	android:name
		ADDED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-103
service#io.invertase.firebase.messaging.ReactNativeFirebaseMessagingService
ADDED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-21:19
	android:exported
		ADDED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-37
	android:name
		ADDED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-95
receiver#io.invertase.firebase.messaging.ReactNativeFirebaseMessagingReceiver
ADDED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-30:20
	android:exported
		ADDED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-36
	android:permission
		ADDED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-73
	android:name
		ADDED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-96
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-29:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-81
	android:name
		ADDED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-78
meta-data#delivery_metrics_exported_to_big_query_enabled
ADDED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:9-34:37
	android:value
		ADDED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-34
	android:name
		ADDED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-74
meta-data#firebase_messaging_auto_init_enabled
ADDED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-37:36
	android:value
		ADDED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-33
	android:name
		ADDED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-64
meta-data#firebase_messaging_notification_delegation_enabled
ADDED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:9-40:37
	android:value
		ADDED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-34
	android:name
		ADDED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-78
meta-data#com.google.firebase.messaging.default_notification_color
ADDED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:9-46:47
	android:resource
		ADDED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:46:13-44
	android:name
		ADDED from [:react-native-firebase_messaging] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-84
meta-data#app_data_collection_default_enabled
ADDED from [:react-native-firebase_app] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:36
	android:value
		ADDED from [:react-native-firebase_app] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-33
	android:name
		ADDED from [:react-native-firebase_app] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-63
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [:react-native-firebase_app] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:19
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-crashlytics-ndk:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\aeb8af0b6421a1d3b2ea7118ef2989f3\transformed\jetified-firebase-crashlytics-ndk-19.4.4\AndroidManifest.xml:29:9-35:19
MERGED from [com.google.firebase:firebase-crashlytics-ndk:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\aeb8af0b6421a1d3b2ea7118ef2989f3\transformed\jetified-firebase-crashlytics-ndk-19.4.4\AndroidManifest.xml:29:9-35:19
MERGED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1756d0e485a053b668d9b50ad4f3232e\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1756d0e485a053b668d9b50ad4f3232e\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da392714e2b4001ad5e92291804d3aa6\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:30:9-36:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da392714e2b4001ad5e92291804d3aa6\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:30:9-36:19
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a460d6b3936d3e351c6512e5610dcf7\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:26:9-35:19
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a460d6b3936d3e351c6512e5610dcf7\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:26:9-35:19
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0462f5e85e6a2044c6a7a07e46b26c72\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0462f5e85e6a2044c6a7a07e46b26c72\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d9b7021a76b8c370dc1a55cf6e33d91\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:27:9-36:19
MERGED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d9b7021a76b8c370dc1a55cf6e33d91\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:27:9-36:19
MERGED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e3d2eaa1583aa636ad7200f2b7ab13e0\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e3d2eaa1583aa636ad7200f2b7ab13e0\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\943c4eb5cff9afe2e09fe6fa01f0c45d\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\943c4eb5cff9afe2e09fe6fa01f0c45d\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e25a4e9ce0a57f7ae5d2347b9eeb0127\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:22:9-31:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e25a4e9ce0a57f7ae5d2347b9eeb0127\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:22:9-31:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7154a9c5f3c74624ae64a9d79896bb9e\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7154a9c5f3c74624ae64a9d79896bb9e\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8bcdce69c2e38ab05a17e87b8e240c89\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8bcdce69c2e38ab05a17e87b8e240c89\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6a23358ad7c61e852a7e630e05b3f0c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6a23358ad7c61e852a7e630e05b3f0c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
	android:exported
		ADDED from [:react-native-firebase_app] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
	tools:targetApi
		ADDED from [:react-native-firebase_app] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-32
	android:directBootAware
		ADDED from [:react-native-firebase_app] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-43
	android:name
		ADDED from [:react-native-firebase_app] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-84
meta-data#com.google.firebase.components:io.invertase.firebase.app.ReactNativeFirebaseAppRegistrar
ADDED from [:react-native-firebase_app] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [:react-native-firebase_app] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [:react-native-firebase_app] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-120
provider#io.invertase.firebase.app.ReactNativeFirebaseAppInitProvider
ADDED from [:react-native-firebase_app] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-27:38
	android:authorities
		ADDED from [:react-native-firebase_app] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-86
	android:exported
		ADDED from [:react-native-firebase_app] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-37
	android:initOrder
		ADDED from [:react-native-firebase_app] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-35
	android:name
		ADDED from [:react-native-firebase_app] C:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-88
queries
ADDED from [:react-native-image-crop-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-11:15
MERGED from [com.pubscale.sdkone:offerwall:1.0.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:16:5-20:15
MERGED from [com.pubscale.sdkone:offerwall:1.0.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:16:5-20:15
MERGED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:10:5-39:15
MERGED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:10:5-39:15
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:35:5-68:15
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:35:5-68:15
MERGED from [androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b587af44bbcbc68fefe9a64cb17155c\transformed\jetified-camera-extensions-1.5.0-alpha03\AndroidManifest.xml:22:5-26:15
MERGED from [androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b587af44bbcbc68fefe9a64cb17155c\transformed\jetified-camera-extensions-1.5.0-alpha03\AndroidManifest.xml:22:5-26:15
intent#action:name:android.media.action.IMAGE_CAPTURE
ADDED from [:react-native-image-crop-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
action#android.media.action.IMAGE_CAPTURE
ADDED from [:react-native-image-crop-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-73
	android:name
		ADDED from [:react-native-image-crop-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-70
provider#com.reactnative.ivpusic.imagepicker.IvpusicImagePickerFileProvider
ADDED from [:react-native-image-crop-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-26:20
	android:grantUriPermissions
		ADDED from [:react-native-image-crop-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-47
	android:authorities
		ADDED from [:react-native-image-crop-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-60
	android:exported
		ADDED from [:react-native-image-crop-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-37
	android:name
		ADDED from [:react-native-image-crop-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-94
activity#com.yalantis.ucrop.UCropActivity
ADDED from [:react-native-image-crop-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:9-30:72
	android:theme
		ADDED from [:react-native-image-crop-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-69
	android:name
		ADDED from [:react-native-image-crop-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-60
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:react-native-image-crop-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:9-44:19
	android:enabled
		ADDED from [:react-native-image-crop-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-36
	android:exported
		ADDED from [:react-native-image-crop-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-37
	android:name
		ADDED from [:react-native-image-crop-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:react-native-image-crop-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-39:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:react-native-image-crop-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-94
	android:name
		ADDED from [:react-native-image-crop-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:25-91
meta-data#photopicker_activity:0:required
ADDED from [:react-native-image-crop-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:13-43:36
	android:value
		ADDED from [:react-native-image-crop-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:17-33
	android:name
		ADDED from [:react-native-image-crop-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:17-63
provider#com.imagepicker.ImagePickerProvider
ADDED from [:react-native-image-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
	android:grantUriPermissions
		ADDED from [:react-native-image-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
	android:authorities
		ADDED from [:react-native-image-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-71
	android:exported
		ADDED from [:react-native-image-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
	android:name
		ADDED from [:react-native-image-picker] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-63
activity#com.razorpay.CheckoutActivity
ADDED from [:react-native-razorpay] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:86
MERGED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:42:9-50:20
MERGED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:42:9-50:20
	android:exported
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:45:13-37
	android:configChanges
		ADDED from [:react-native-razorpay] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-83
	android:theme
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:46:13-49
	android:name
		ADDED from [:react-native-razorpay] C:\A1\adtip-reactnative\Adtip\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-57
activity#com.facebook.react.devsupport.DevSettingsActivity
ADDED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\70054bad7b567e49310c48ab88316206\transformed\jetified-react-android-0.79.2-debug\AndroidManifest.xml:19:9-21:40
	android:exported
		ADDED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\70054bad7b567e49310c48ab88316206\transformed\jetified-react-android-0.79.2-debug\AndroidManifest.xml:21:13-37
	android:name
		ADDED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\70054bad7b567e49310c48ab88316206\transformed\jetified-react-android-0.79.2-debug\AndroidManifest.xml:20:13-77
uses-permission#com.google.android.providers.gsf.permission.READ_GSERVICES
ADDED from [com.pubscale.sdkone:offerwall:1.0.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:11:5-98
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\23055739e0573ccfe469dc5146f384c3\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:5-98
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\23055739e0573ccfe469dc5146f384c3\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:5-98
	android:name
		ADDED from [com.pubscale.sdkone:offerwall:1.0.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:11:22-95
uses-permission#android.permission.PACKAGE_USAGE_STATS
ADDED from [com.pubscale.sdkone:offerwall:1.0.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:12:5-14:47
	tools:ignore
		ADDED from [com.pubscale.sdkone:offerwall:1.0.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:14:9-44
	android:name
		ADDED from [com.pubscale.sdkone:offerwall:1.0.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:13:9-62
intent#action:name:android.intent.action.MAIN
ADDED from [com.pubscale.sdkone:offerwall:1.0.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:17:9-19:18
activity#com.pubscale.sdkone.offerwall.ui.OfferWallActivity
ADDED from [com.pubscale.sdkone:offerwall:1.0.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:23:9-27:65
	android:exported
		ADDED from [com.pubscale.sdkone:offerwall:1.0.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:26:13-37
	android:configChanges
		ADDED from [com.pubscale.sdkone:offerwall:1.0.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:25:13-59
	android:theme
		ADDED from [com.pubscale.sdkone:offerwall:1.0.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:27:13-62
	android:name
		ADDED from [com.pubscale.sdkone:offerwall:1.0.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:24:13-78
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2dd03a2da4e4e80325824a48077809a5\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2dd03a2da4e4e80325824a48077809a5\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
intent#action:name:android.intent.action.VIEW+data:mimeType:*/*+data:scheme:*
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:11:9-17:18
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:host:pay+data:mimeType:*/*+data:scheme:upi
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:18:9-27:18
intent#action:name:android.intent.action.SEND+data:mimeType:*/*
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:31:9-35:18
action#android.intent.action.SEND
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:32:13-65
	android:name
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:32:21-62
intent#action:name:rzp.device_token.share
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:36:9-38:18
action#rzp.device_token.share
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:37:13-61
	android:name
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:37:21-58
intent-filter#action:name:android.intent.action.MAIN
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:47:13-49:29
provider#androidx.startup.InitializationProvider
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:52:9-60:20
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c02c6a58638664cbfa4f398f77214d5c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c02c6a58638664cbfa4f398f77214d5c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\88ef7ff10d1cfe00bba6c2d4e1214614\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\88ef7ff10d1cfe00bba6c2d4e1214614\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ad6a5f68ee830401b49f7ec6ef6ee94\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ad6a5f68ee830401b49f7ec6ef6ee94\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:56:13-31
	android:authorities
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:54:13-68
	android:exported
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:55:13-37
	android:name
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:53:13-67
meta-data#com.razorpay.RazorpayInitializer
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:57:13-59:52
	android:value
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:59:17-49
	android:name
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:58:17-64
activity#com.razorpay.MagicXActivity
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:62:9-65:75
	android:exported
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:64:13-37
	android:theme
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:65:13-72
	android:name
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:63:13-55
meta-data#com.razorpay.plugin.googlepay_all
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:67:9-69:58
	android:value
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:69:13-55
	android:name
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:68:13-61
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.ndk.CrashlyticsNdkRegistrar
ADDED from [com.google.firebase:firebase-crashlytics-ndk:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\aeb8af0b6421a1d3b2ea7118ef2989f3\transformed\jetified-firebase-crashlytics-ndk-19.4.4\AndroidManifest.xml:32:13-34:85
	android:value
		ADDED from [com.google.firebase:firebase-crashlytics-ndk:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\aeb8af0b6421a1d3b2ea7118ef2989f3\transformed\jetified-firebase-crashlytics-ndk-19.4.4\AndroidManifest.xml:34:17-82
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics-ndk:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\aeb8af0b6421a1d3b2ea7118ef2989f3\transformed\jetified-firebase-crashlytics-ndk-19.4.4\AndroidManifest.xml:33:17-122
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar
ADDED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1756d0e485a053b668d9b50ad4f3232e\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1756d0e485a053b668d9b50ad4f3232e\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1756d0e485a053b668d9b50ad4f3232e\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:16:17-126
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar
ADDED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1756d0e485a053b668d9b50ad4f3232e\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1756d0e485a053b668d9b50ad4f3232e\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1756d0e485a053b668d9b50ad4f3232e\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:19:17-115
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da392714e2b4001ad5e92291804d3aa6\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:28:5-88
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:28:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e34b30e2c3807979c179fdca07abbb5d\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e34b30e2c3807979c179fdca07abbb5d\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da392714e2b4001ad5e92291804d3aa6\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da392714e2b4001ad5e92291804d3aa6\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e34b30e2c3807979c179fdca07abbb5d\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e34b30e2c3807979c179fdca07abbb5d\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da392714e2b4001ad5e92291804d3aa6\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:27:22-79
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da392714e2b4001ad5e92291804d3aa6\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:33:13-35:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da392714e2b4001ad5e92291804d3aa6\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:35:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da392714e2b4001ad5e92291804d3aa6\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:34:17-139
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
meta-data#com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar
ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a460d6b3936d3e351c6512e5610dcf7\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
	android:value
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a460d6b3936d3e351c6512e5610dcf7\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:31:17-82
	android:name
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a460d6b3936d3e351c6512e5610dcf7\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:30:17-120
meta-data#com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar
ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a460d6b3936d3e351c6512e5610dcf7\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
	android:value
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a460d6b3936d3e351c6512e5610dcf7\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:34:17-82
	android:name
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a460d6b3936d3e351c6512e5610dcf7\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:33:17-109
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar
ADDED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0462f5e85e6a2044c6a7a07e46b26c72\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0462f5e85e6a2044c6a7a07e46b26c72\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0462f5e85e6a2044c6a7a07e46b26c72\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:18:17-122
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar
ADDED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0462f5e85e6a2044c6a7a07e46b26c72\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0462f5e85e6a2044c6a7a07e46b26c72\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0462f5e85e6a2044c6a7a07e46b26c72\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:21:17-111
meta-data#com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar
ADDED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d9b7021a76b8c370dc1a55cf6e33d91\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:30:13-32:85
	android:value
		ADDED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d9b7021a76b8c370dc1a55cf6e33d91\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:32:17-82
	android:name
		ADDED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d9b7021a76b8c370dc1a55cf6e33d91\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:31:17-118
meta-data#com.google.firebase.components:com.google.firebase.storage.StorageRegistrar
ADDED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d9b7021a76b8c370dc1a55cf6e33d91\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:33:13-35:85
	android:value
		ADDED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d9b7021a76b8c370dc1a55cf6e33d91\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:35:17-82
	android:name
		ADDED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d9b7021a76b8c370dc1a55cf6e33d91\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:34:17-107
service#com.google.firebase.sessions.SessionLifecycleService
ADDED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e3d2eaa1583aa636ad7200f2b7ab13e0\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:22:9-25:40
	android:enabled
		ADDED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e3d2eaa1583aa636ad7200f2b7ab13e0\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:24:13-35
	android:exported
		ADDED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e3d2eaa1583aa636ad7200f2b7ab13e0\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e3d2eaa1583aa636ad7200f2b7ab13e0\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:23:13-80
meta-data#com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar
ADDED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e3d2eaa1583aa636ad7200f2b7ab13e0\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:29:13-31:85
	android:value
		ADDED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e3d2eaa1583aa636ad7200f2b7ab13e0\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:31:17-82
	android:name
		ADDED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e3d2eaa1583aa636ad7200f2b7ab13e0\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:30:17-117
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\943c4eb5cff9afe2e09fe6fa01f0c45d\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\943c4eb5cff9afe2e09fe6fa01f0c45d\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\943c4eb5cff9afe2e09fe6fa01f0c45d\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\943c4eb5cff9afe2e09fe6fa01f0c45d\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\943c4eb5cff9afe2e09fe6fa01f0c45d\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\943c4eb5cff9afe2e09fe6fa01f0c45d\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar
ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e25a4e9ce0a57f7ae5d2347b9eeb0127\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e25a4e9ce0a57f7ae5d2347b9eeb0127\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e25a4e9ce0a57f7ae5d2347b9eeb0127\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
meta-data#com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar
ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e25a4e9ce0a57f7ae5d2347b9eeb0127\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
	android:value
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e25a4e9ce0a57f7ae5d2347b9eeb0127\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
	android:name
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e25a4e9ce0a57f7ae5d2347b9eeb0127\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7154a9c5f3c74624ae64a9d79896bb9e\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7154a9c5f3c74624ae64a9d79896bb9e\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7154a9c5f3c74624ae64a9d79896bb9e\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
meta-data#com.bumptech.glide.integration.okhttp3.OkHttpGlideModule
ADDED from [com.github.bumptech.glide:okhttp3-integration:4.14.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\819885695693bbcd94c9388920867bc1\transformed\jetified-okhttp3-integration-4.14.2\AndroidManifest.xml:10:9-12:43
	android:value
		ADDED from [com.github.bumptech.glide:okhttp3-integration:4.14.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\819885695693bbcd94c9388920867bc1\transformed\jetified-okhttp3-integration-4.14.2\AndroidManifest.xml:12:13-40
	android:name
		ADDED from [com.github.bumptech.glide:okhttp3-integration:4.14.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\819885695693bbcd94c9388920867bc1\transformed\jetified-okhttp3-integration-4.14.2\AndroidManifest.xml:11:13-84
service#com.google.mlkit.common.internal.MlKitComponentDiscoveryService
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\72b0f839adbd62970af4fc893cad0618\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ac0cc6b1d80a9a5d39c72b858381152\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ac0cc6b1d80a9a5d39c72b858381152\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9abbf9a46284e5d98ed99dd592794407\transformed\jetified-common-18.11.0\AndroidManifest.xml:15:9-23:19
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9abbf9a46284e5d98ed99dd592794407\transformed\jetified-common-18.11.0\AndroidManifest.xml:15:9-23:19
	android:exported
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\72b0f839adbd62970af4fc893cad0618\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:11:13-37
	tools:targetApi
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9abbf9a46284e5d98ed99dd592794407\transformed\jetified-common-18.11.0\AndroidManifest.xml:19:13-32
	android:directBootAware
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9abbf9a46284e5d98ed99dd592794407\transformed\jetified-common-18.11.0\AndroidManifest.xml:17:13-43
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\72b0f839adbd62970af4fc893cad0618\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:10:13-91
meta-data#com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\72b0f839adbd62970af4fc893cad0618\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\72b0f839adbd62970af4fc893cad0618\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\72b0f839adbd62970af4fc893cad0618\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:13:17-120
meta-data#com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar
ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ac0cc6b1d80a9a5d39c72b858381152\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ac0cc6b1d80a9a5d39c72b858381152\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ac0cc6b1d80a9a5d39c72b858381152\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:13:17-124
provider#com.google.mlkit.common.internal.MlKitInitProvider
ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9abbf9a46284e5d98ed99dd592794407\transformed\jetified-common-18.11.0\AndroidManifest.xml:9:9-13:38
	android:authorities
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9abbf9a46284e5d98ed99dd592794407\transformed\jetified-common-18.11.0\AndroidManifest.xml:11:13-69
	android:exported
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9abbf9a46284e5d98ed99dd592794407\transformed\jetified-common-18.11.0\AndroidManifest.xml:12:13-37
	android:initOrder
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9abbf9a46284e5d98ed99dd592794407\transformed\jetified-common-18.11.0\AndroidManifest.xml:13:13-35
	android:name
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9abbf9a46284e5d98ed99dd592794407\transformed\jetified-common-18.11.0\AndroidManifest.xml:10:13-78
meta-data#com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar
ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9abbf9a46284e5d98ed99dd592794407\transformed\jetified-common-18.11.0\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9abbf9a46284e5d98ed99dd592794407\transformed\jetified-common-18.11.0\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9abbf9a46284e5d98ed99dd592794407\transformed\jetified-common-18.11.0\AndroidManifest.xml:21:17-120
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8bcdce69c2e38ab05a17e87b8e240c89\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8bcdce69c2e38ab05a17e87b8e240c89\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8bcdce69c2e38ab05a17e87b8e240c89\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b5b5af68370d2b9a9c88be9dd6fee4c\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bebd9d4895328a627b6a12462063f75b\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:34:9-36:40
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bebd9d4895328a627b6a12462063f75b\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:34:9-36:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b5b5af68370d2b9a9c88be9dd6fee4c\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b5b5af68370d2b9a9c88be9dd6fee4c\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:27:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b5b5af68370d2b9a9c88be9dd6fee4c\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:29:13-31:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b5b5af68370d2b9a9c88be9dd6fee4c\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:31:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b5b5af68370d2b9a9c88be9dd6fee4c\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:30:17-94
uses-permission#android.permission.ACCESS_ADSERVICES_TOPICS
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:29:5-83
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:29:22-80
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:38:9-44:18
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:47:9-49:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:48:13-90
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:48:21-87
intent#action:name:android.intent.action.INSERT+data:mimeType:vnd.android.cursor.dir/event
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:52:9-56:18
action#android.intent.action.INSERT
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:53:13-67
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:53:21-64
intent#action:name:android.intent.action.VIEW+data:scheme:sms
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:57:9-61:18
intent#action:name:android.intent.action.DIAL+data:path:tel:
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:62:9-66:18
action#android.intent.action.DIAL
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:63:13-65
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:63:21-62
activity#com.google.android.gms.ads.AdActivity
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:73:9-78:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:76:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:78:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:75:13-122
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:77:13-61
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:74:13-65
provider#com.google.android.gms.ads.MobileAdsInitProvider
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:80:9-85:43
	android:authorities
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:82:13-73
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:83:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:85:13-40
	android:initOrder
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:84:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:81:13-76
service#com.google.android.gms.ads.AdService
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:87:9-91:43
	android:enabled
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:89:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:90:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:91:13-40
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:88:13-64
activity#com.google.android.gms.ads.OutOfContextTestingActivity
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:93:9-97:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:96:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:97:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:95:13-122
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:94:13-82
activity#com.google.android.gms.ads.NotificationHandlerActivity
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:98:9-105:43
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:100:13-46
	android:launchMode
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:102:13-44
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:101:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:105:13-40
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:104:13-72
	android:taskAffinity
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:103:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:99:13-82
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e2acb9bee0a5b872b1d515edf8b6f46\transformed\room-runtime-2.5.2\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e2acb9bee0a5b872b1d515edf8b6f46\transformed\room-runtime-2.5.2\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e2acb9bee0a5b872b1d515edf8b6f46\transformed\room-runtime-2.5.2\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e2acb9bee0a5b872b1d515edf8b6f46\transformed\room-runtime-2.5.2\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e2acb9bee0a5b872b1d515edf8b6f46\transformed\room-runtime-2.5.2\AndroidManifest.xml:25:13-74
service#androidx.credentials.playservices.CredentialProviderMetadataHolder
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:28:13-60
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
meta-data#androidx.credentials.CREDENTIAL_PROVIDER_KEY
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
	android:value
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
activity#androidx.credentials.playservices.HiddenActivity
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
	android:fitsSystemWindows
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
	android:configChanges
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
	android:theme
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
intent#action:name:androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b587af44bbcbc68fefe9a64cb17155c\transformed\jetified-camera-extensions-1.5.0-alpha03\AndroidManifest.xml:23:9-25:18
action#androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b587af44bbcbc68fefe9a64cb17155c\transformed\jetified-camera-extensions-1.5.0-alpha03\AndroidManifest.xml:24:13-86
	android:name
		ADDED from [androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b587af44bbcbc68fefe9a64cb17155c\transformed\jetified-camera-extensions-1.5.0-alpha03\AndroidManifest.xml:24:21-83
uses-library#androidx.camera.extensions.impl
ADDED from [androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b587af44bbcbc68fefe9a64cb17155c\transformed\jetified-camera-extensions-1.5.0-alpha03\AndroidManifest.xml:29:9-31:40
	android:required
		ADDED from [androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b587af44bbcbc68fefe9a64cb17155c\transformed\jetified-camera-extensions-1.5.0-alpha03\AndroidManifest.xml:31:13-37
	android:name
		ADDED from [androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b587af44bbcbc68fefe9a64cb17155c\transformed\jetified-camera-extensions-1.5.0-alpha03\AndroidManifest.xml:30:13-59
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\012659820a206ff178b1dfc31d8ed905\transformed\jetified-camera-camera2-1.5.0-alpha03\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\cab56218d538a3aaadf18b40d63683ba\transformed\jetified-camera-core-1.5.0-alpha03\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\cab56218d538a3aaadf18b40d63683ba\transformed\jetified-camera-core-1.5.0-alpha03\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\012659820a206ff178b1dfc31d8ed905\transformed\jetified-camera-camera2-1.5.0-alpha03\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\012659820a206ff178b1dfc31d8ed905\transformed\jetified-camera-camera2-1.5.0-alpha03\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\012659820a206ff178b1dfc31d8ed905\transformed\jetified-camera-camera2-1.5.0-alpha03\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\012659820a206ff178b1dfc31d8ed905\transformed\jetified-camera-camera2-1.5.0-alpha03\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\012659820a206ff178b1dfc31d8ed905\transformed\jetified-camera-camera2-1.5.0-alpha03\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\012659820a206ff178b1dfc31d8ed905\transformed\jetified-camera-camera2-1.5.0-alpha03\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\012659820a206ff178b1dfc31d8ed905\transformed\jetified-camera-camera2-1.5.0-alpha03\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\012659820a206ff178b1dfc31d8ed905\transformed\jetified-camera-camera2-1.5.0-alpha03\AndroidManifest.xml:31:17-103
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84319fb3abfb9f4cf22d670fd740c51d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84319fb3abfb9f4cf22d670fd740c51d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84319fb3abfb9f4cf22d670fd740c51d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84319fb3abfb9f4cf22d670fd740c51d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84319fb3abfb9f4cf22d670fd740c51d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84319fb3abfb9f4cf22d670fd740c51d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84319fb3abfb9f4cf22d670fd740c51d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84319fb3abfb9f4cf22d670fd740c51d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84319fb3abfb9f4cf22d670fd740c51d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84319fb3abfb9f4cf22d670fd740c51d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:34:13-89
meta-data#com.bumptech.glide.integration.webp.WebpGlideModule
ADDED from [com.github.zjupure:webpdecoder:2.6.4.14.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\28d9696d1ba03482c83101bc9186888b\transformed\jetified-webpdecoder-2.6.4.14.2\AndroidManifest.xml:10:9-12:43
	android:value
		ADDED from [com.github.zjupure:webpdecoder:2.6.4.14.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\28d9696d1ba03482c83101bc9186888b\transformed\jetified-webpdecoder-2.6.4.14.2\AndroidManifest.xml:12:13-40
	android:name
		ADDED from [com.github.zjupure:webpdecoder:2.6.4.14.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\28d9696d1ba03482c83101bc9186888b\transformed\jetified-webpdecoder-2.6.4.14.2\AndroidManifest.xml:11:13-79
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3d93af0b117ed25b95c723ac599fee1\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3d93af0b117ed25b95c723ac599fee1\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:40:13-87
uses-feature#0x00020000
ADDED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68f1c6167ded75c22bf466a708dea38f\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:26:5-28:35
	android:glEsVersion
		ADDED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68f1c6167ded75c22bf466a708dea38f\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:27:9-41
	android:required
		ADDED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68f1c6167ded75c22bf466a708dea38f\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:28:9-32
uses-library#org.apache.http.legacy
ADDED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68f1c6167ded75c22bf466a708dea38f\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:33:9-35:40
	android:required
		ADDED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68f1c6167ded75c22bf466a708dea38f\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:35:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68f1c6167ded75c22bf466a708dea38f\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:34:13-50
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\31cc88bd1a811e01477d23dfca8d3955\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\31cc88bd1a811e01477d23dfca8d3955\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\31cc88bd1a811e01477d23dfca8d3955\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\31cc88bd1a811e01477d23dfca8d3955\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6a23358ad7c61e852a7e630e05b3f0c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6a23358ad7c61e852a7e630e05b3f0c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6a23358ad7c61e852a7e630e05b3f0c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6a23358ad7c61e852a7e630e05b3f0c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6a23358ad7c61e852a7e630e05b3f0c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6a23358ad7c61e852a7e630e05b3f0c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6a23358ad7c61e852a7e630e05b3f0c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6a23358ad7c61e852a7e630e05b3f0c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6a23358ad7c61e852a7e630e05b3f0c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c02c6a58638664cbfa4f398f77214d5c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c02c6a58638664cbfa4f398f77214d5c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c02c6a58638664cbfa4f398f77214d5c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4f7aaf1dd3251b595a6d29fbf072499\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4f7aaf1dd3251b595a6d29fbf072499\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4f7aaf1dd3251b595a6d29fbf072499\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.adtip.app.adtip_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4f7aaf1dd3251b595a6d29fbf072499\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4f7aaf1dd3251b595a6d29fbf072499\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4f7aaf1dd3251b595a6d29fbf072499\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4f7aaf1dd3251b595a6d29fbf072499\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4f7aaf1dd3251b595a6d29fbf072499\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.adtip.app.adtip_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4f7aaf1dd3251b595a6d29fbf072499\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4f7aaf1dd3251b595a6d29fbf072499\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\88ef7ff10d1cfe00bba6c2d4e1214614\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\88ef7ff10d1cfe00bba6c2d4e1214614\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\88ef7ff10d1cfe00bba6c2d4e1214614\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\32c6230c0475f108e440ef839e9bcc70\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\32c6230c0475f108e440ef839e9bcc70\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\32c6230c0475f108e440ef839e9bcc70\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ee598c7bab1dd2859d5d01f2229eaf5\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ee598c7bab1dd2859d5d01f2229eaf5\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ee598c7bab1dd2859d5d01f2229eaf5\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bebd9d4895328a627b6a12462063f75b\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:24:9-28:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bebd9d4895328a627b6a12462063f75b\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:26:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bebd9d4895328a627b6a12462063f75b\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:27:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bebd9d4895328a627b6a12462063f75b\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:25:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bebd9d4895328a627b6a12462063f75b\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:30:9-32:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bebd9d4895328a627b6a12462063f75b\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bebd9d4895328a627b6a12462063f75b\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:31:13-132
uses-permission#android.permission.SCHEDULE_EXACT_ALARM
ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:16:5-79
	android:name
		ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:16:22-76
uses-permission#android.permission.BROADCAST_CLOSE_SYSTEM_DIALOGS
ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:17:5-19:38
	android:maxSdkVersion
		ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:19:9-35
	android:name
		ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:18:9-73
uses-permission#android.permission.ACCESS_NOTIFICATION_POLICY
ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:21:5-23:38
	android:minSdkVersion
		ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:23:9-35
	android:name
		ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:22:9-69
service#app.notifee.core.ReceiverService
ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:28:9-30:40
	android:exported
		ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:29:13-60
activity#app.notifee.core.NotificationReceiverActivity
ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:32:9-38:75
	android:excludeFromRecents
		ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:34:13-46
	android:noHistory
		ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:36:13-37
	android:exported
		ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:35:13-36
	android:theme
		ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:38:13-72
	android:taskAffinity
		ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:37:13-36
	android:name
		ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:33:13-73
receiver#app.notifee.core.RebootBroadcastReceiver
ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:46:9-54:20
	android:exported
		ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:48:13-37
	android:name
		ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:47:13-68
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.QUICKBOOT_POWERON+action:name:com.htc.intent.action.QUICKBOOT_POWERON
ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:49:13-53:29
action#android.intent.action.QUICKBOOT_POWERON
ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:51:17-82
	android:name
		ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:51:25-79
action#com.htc.intent.action.QUICKBOOT_POWERON
ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:52:17-82
	android:name
		ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:52:25-79
receiver#app.notifee.core.AlarmPermissionBroadcastReceiver
ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:55:9-61:20
	android:exported
		ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:57:13-36
	android:name
		ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:56:13-77
intent-filter#action:name:android.app.action.SCHEDULE_EXACT_ALARM_PERMISSION_STATE_CHANGED
ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:58:13-60:29
action#android.app.action.SCHEDULE_EXACT_ALARM_PERMISSION_STATE_CHANGED
ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:59:17-107
	android:name
		ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:59:25-104
receiver#app.notifee.core.NotificationAlarmReceiver
ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:62:9-70:20
	android:exported
		ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:64:13-37
	android:name
		ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:63:13-70
receiver#app.notifee.core.BlockStateBroadcastReceiver
ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:73:9-81:20
	android:exported
		ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:75:13-37
	android:name
		ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:74:13-72
intent-filter#action:name:android.app.action.APP_BLOCK_STATE_CHANGED+action:name:android.app.action.NOTIFICATION_CHANNEL_BLOCK_STATE_CHANGED+action:name:android.app.action.NOTIFICATION_CHANNEL_GROUP_BLOCK_STATE_CHANGED
ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:76:13-80:29
action#android.app.action.APP_BLOCK_STATE_CHANGED
ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:77:17-85
	android:name
		ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:77:25-82
action#android.app.action.NOTIFICATION_CHANNEL_BLOCK_STATE_CHANGED
ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:78:17-102
	android:name
		ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:78:25-99
action#android.app.action.NOTIFICATION_CHANNEL_GROUP_BLOCK_STATE_CHANGED
ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:79:17-108
	android:name
		ADDED from [app.notifee:core:202108261754] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:79:25-105
activity#com.jakewharton.processphoenix.ProcessPhoenix
ADDED from [com.jakewharton:process-phoenix:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1414cc992efd0fca4a6cd3c9d41ee4a2\transformed\jetified-process-phoenix-2.1.2\AndroidManifest.xml:8:9-12:75
	android:process
		ADDED from [com.jakewharton:process-phoenix:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1414cc992efd0fca4a6cd3c9d41ee4a2\transformed\jetified-process-phoenix-2.1.2\AndroidManifest.xml:11:13-39
	android:exported
		ADDED from [com.jakewharton:process-phoenix:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1414cc992efd0fca4a6cd3c9d41ee4a2\transformed\jetified-process-phoenix-2.1.2\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [com.jakewharton:process-phoenix:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1414cc992efd0fca4a6cd3c9d41ee4a2\transformed\jetified-process-phoenix-2.1.2\AndroidManifest.xml:12:13-72
	android:name
		ADDED from [com.jakewharton:process-phoenix:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1414cc992efd0fca4a6cd3c9d41ee4a2\transformed\jetified-process-phoenix-2.1.2\AndroidManifest.xml:9:13-73
meta-data#com.facebook.soloader.enabled
ADDED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\32503beb4641a05905d0ff4f11c56937\transformed\jetified-soloader-0.12.1\AndroidManifest.xml:12:9-14:37
	android:value
		ADDED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\32503beb4641a05905d0ff4f11c56937\transformed\jetified-soloader-0.12.1\AndroidManifest.xml:14:13-34
	android:name
		ADDED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\32503beb4641a05905d0ff4f11c56937\transformed\jetified-soloader-0.12.1\AndroidManifest.xml:13:13-57
activity#com.google.android.play.core.common.PlayCoreDialogWrapperActivity
ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c2b541c81d6853fcbc79ca7ed76949c\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
	android:stateNotNeeded
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c2b541c81d6853fcbc79ca7ed76949c\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
	android:exported
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c2b541c81d6853fcbc79ca7ed76949c\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
	android:theme
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c2b541c81d6853fcbc79ca7ed76949c\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
	android:name
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c2b541c81d6853fcbc79ca7ed76949c\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
