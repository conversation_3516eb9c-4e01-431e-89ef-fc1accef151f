# C/C++ build system timings
generate_cxx_metadata
  [gap of 464ms]
  create-invalidation-state 107ms
  generate-prefab-packages
    [gap of 120ms]
    exec-prefab 4729ms
    [gap of 270ms]
  generate-prefab-packages completed in 5119ms
  execute-generate-process
    [gap of 64ms]
    exec-configure 9997ms
    [gap of 420ms]
  execute-generate-process completed in 10481ms
  [gap of 110ms]
  write-metadata-json-to-file 56ms
generate_cxx_metadata completed in 16408ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 97ms]
  create-invalidation-state 340ms
  [gap of 131ms]
  write-metadata-json-to-file 52ms
generate_cxx_metadata completed in 622ms

